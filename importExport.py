import re
import os
import sys
from pathlib import Path

import cmdx
import maya.mel as mel
import maya.cmds as cmds
import maya.api.OpenMaya as om

from PySide6 import QtWidgets  # for QApplication.clipboard()

from .deformers import get_deformers_from_object
from .utils import obj_is_type, timing, load_plugin, is_selection_geometry_node

# _____________________________________ GLOBAL VARS  _____________________________________

TEMP_DIR = Path(cmds.internalVar(userTmpDir=True))
CLIPBOARD_OBJ = TEMP_DIR / 'clipboard_objects.obj'
CLIPBOARD_USD = TEMP_DIR / 'clipboard_objects.usd'
CLIPBOARD_INFO = TEMP_DIR / 'clipboard_objects_info.json'
CLIPBOARD_CURVES_ABC = TEMP_DIR / 'clipboard_curves.abc'
NVIL_CLIPBOARD_OBJ = Path('C:/Users/<USER>/AppData/Roaming/DigitalFossils/NVil/Media/Clipboard/ClipboardObj.obj')

# _____________________________________ USD  _____________________________________

def remove_extra_transforms(parent_transform):
	"""
	USD exported from Blender has extra transforms

	 - eg: Object "Cube" with data "Cube.001" will be imported as
	       Cube (Extra Transform)
				 	- Cube.001 (Mesh)
	This script attempt to remove the extra transform so that the result is
				 Cube (Mesh)
					(Note: mesh is renamed to the extra Transform's name)
	"""
	if obj_is_type(parent_transform, 'joint'):
		return None
	children = cmds.listRelatives(parent_transform, children=True, type="transform", fullPath=True)
	if not children:
		return None
	#print(f'remove_extra_transforms: root_transform:{root_transform}, children:{children}')
	for child in children:
		sub_children = cmds.listRelatives(child, children=True, type="transform", fullPath=True)
		#print(f'  > child:{child}, sub_children:{sub_children}')
		# Check if it only has one child and the child is a transform
		if sub_children and len(sub_children) == 1:
			if obj_is_type(sub_children, 'joint'):
				return None
			child_name = child.split('|')[-1]
			reparented_obj = cmds.parent(sub_children, parent_transform)
			cmds.delete(child)
			new_name = cmds.rename(reparented_obj, child_name)
			remove_extra_transforms(new_name)
		elif sub_children:
			for child_ in sub_children:
				if obj_is_type(sub_children, 'joint'):
					return None
				remove_extra_transforms(child_)

@timing
def import_clipboard_usd():
	load_plugin('mayaUsdPlugin')
	top_level_transforms_before = cmds.ls(assemblies=True)
	if CLIPBOARD_USD.is_file():
		cmds.mayaUSDImport(file=CLIPBOARD_USD.as_posix(), primPath='/')
		top_level_transforms_after = cmds.ls(assemblies=True)
		imported = set(top_level_transforms_after) - set(top_level_transforms_before)
		for obj in imported:
			remove_extra_transforms(obj)
		cmds.select(imported)
		# for obj in imported:
		# 	new_name = re.sub(f"^{re.escape('import_')}", '', obj)
		# 	cmds.rename(obj, new_name)

@timing
def export_usd(filepath, usd_export_options):
	cmds.file(CLIPBOARD_USD, pr=True, force=True, options=usd_export_options, type="USD Export", es=True)

def export_clipboard_usd():
	load_plugin('mayaUsdPlugin')

	if not cmds.ls(sl=True):
		return None

	def find_joint_or_blendshape(obj):
		if cmds.nodeType(obj) == 'joint':
			return 'joint'
		for deformer in get_deformers_from_object(obj) or []:
			if cmds.nodeType(deformer) == 'blendShape':
				return 'blendShape'

	joint_or_blendshape = None

	selections = cmds.ls(sl=True, objectsOnly=True, transforms=True, long=True)
	for selection in selections:
		joint_or_blendshape = find_joint_or_blendshape(selection)
		if joint_or_blendshape:
			break
		children = cmds.listRelatives(selection, allDescendents=True, fullPath=True)
		for child in children:
			joint_or_blendshape = find_joint_or_blendshape(child)
			if joint_or_blendshape:
				break
		if joint_or_blendshape:
				break
	
	result = ""

	if joint_or_blendshape:
		result = cmds.confirmDialog(title=f'Confirm',
														message=f'Do you want to export {joint_or_blendshape}?',
														button=['Yes', 'No'], defaultButton='Yes', cancelButton='No', dismissString='No')
	if result == "Yes":
		usd_export_options = "exportSkels=auto;exportSkin=auto;exportBlendShapes=1;rootPrim=root;rootPrimType=xform;defaultPrim=root"
	else:
		usd_export_options = ""
	
	export_usd(CLIPBOARD_USD, usd_export_options)

	# cmds.mayaUSDExport(file=CLIPBOARD_USD.as_posix(),
	# 									selection=True,
	# 									exportSkels='auto',
	# 									exportSkin='auto',
	# 									exportBlendShapes=True,
	# 									rootPrim='root',
	# 									defaultPrim='root',
	# 									rootPrimType='Xform')

# _____________________________________ MAYA TMP.ma/.mb  _____________________________________

def get_last_modified_tmp_maya_file():
	latest_maya_tmp_file = None
	latest_mtime = 0
	for each_file in TEMP_DIR.glob("*.m[ab]"):
		mtime = each_file.stat().st_mtime
		if mtime > latest_mtime:
			latest_mtime = mtime
			latest_maya_tmp_file = each_file
	return latest_maya_tmp_file

def import_tmp():
	# import last modified tmp.m* in tmp folder
	latest_maya_tmp_file = get_last_modified_tmp_maya_file()
	if latest_maya_tmp_file:
		ext = latest_maya_tmp_file.suffix
		ext_map = {'.ma':'mayaAscii', '.mb':'mayaBinary'}
		mel_command = 'file -import -type "{}" -ignoreVersion -mergeNamespacesOnClash 0 -rpr "tmp" -options "v=0;" "{}";'.format(ext_map[ext], latest_maya_tmp_file.as_posix())
		print(mel_command)
		mel.eval(mel_command)

def export_tmp():
	selections = cmds.ls(sl=True)
	
	if selections:
		current_scene_path = cmds.file(q=True, sn=True)
		ext_map = {'.ma':'mayaAscii', '.mb':'mayaBinary'}
		ext = '.mb'
		if current_scene_path:
			dummy_path, ext = os.path.splitext(current_scene_path)
		tmp_file = "C:/Users/<USER>/AppData/Local/Temp/tmp" + ext
		mel_command = 'file -force -options "v=0;" -typ "{}" -pr -es "{}"'.format(ext_map[ext], tmp_file)
		mel.eval(mel_command)

# _____________________________________ ALEMBIC CURVES  _____________________________________

def import_clipboard_alembic_curves():
	load_plugin('AbcImport')

	all_transforms_before = cmdx.ls(et='transform', assemblies=True)
	cmds.AbcImport(str(CLIPBOARD_CURVES_ABC))
	all_transforms_after = cmdx.ls(et='transform', assemblies=True)

	imported_objs = list(set(all_transforms_after) - set(all_transforms_before))
	imported_objs.sort()

	if imported_objs:
		# Rebuild Curves imported from Blender's Alembic file format
		# cmds.curve( 'curve1', r=True, p=[(0, 0, 0), (3, 5, 6), (10, 12, 14), (9, 9, 9)] )
		print(f"\n>> Imported {len(imported_objs)} objects from {CLIPBOARD_CURVES_ABC}")

		new_curves = []

		for o in imported_objs:
			if o.shape().type() == 'nurbsCurve':
				curveFn = om.MFnNurbsCurve( o.shape().dagPath() )
				point_position_list = []
				for i in range( 0, curveFn.numCVs ):
					point_position_list.append( tuple(curveFn.cvPosition(i))[:3] )
				new_curve = cmds.curve( p=point_position_list )
				new_curves.append( new_curve )
				curve_name = o.name()
				cmdx.delete(o)
				cmds.rename( new_curve, curve_name )

		cmds.select(new_curves)

def export_clipboard_alembic_curves():
	load_plugin('AbcExport')

	root_str = ''
	num_exported_curves = 0
	for o in cmdx.ls(sl=True):
		if o.type() == 'transform' and o.shape() and o.shape().type() == 'nurbsCurve':
			root_str += ' -root ' + o.path()
			num_exported_curves += 1

	if root_str:
		command = '-framerange 1 1 -worldSpace ' + root_str + ' -f ' + str(CLIPBOARD_CURVES_ABC)
		cmds.AbcExport(j=command)
		print(f"\n>> Exported {num_exported_curves} curves to {CLIPBOARD_CURVES_ABC}")

# _____________________________________ OBJ  _____________________________________

def import_obj(filepath, ignoreMat=True):
	""" import_obj(str) -> returns names of imported objects
	"""
	load_plugin('objExport.mll')

	filepath_noext, ext = os.path.splitext(filepath.resolve()) # 'c:\hello\test.txt' -> ('c:\\hello\test', '.txt')
	filename = filepath.name

	scene_transforms_before = cmds.ls(et='transform', long=True)

	# Import the obj file
	mel_import_command = f'file -import -type "OBJ" -ignoreVersion -ra true -mergeNamespacesOnClash false -rpr "import" -options "mo=1" -pr "{filepath.as_posix()}"'

	if ignoreMat:
		# Rename the .mtl file to .123 temporary, to forcefully avoid importing material
		mtl_file = filepath_noext + '.mtl'
		mtl_file_123 = mtl_file + '.123'
		if os.path.isfile(mtl_file):
			if os.path.isfile(mtl_file_123):
				os.remove(mtl_file_123)
			os.rename(mtl_file, mtl_file_123)

		# <The Main Obj Import>
		mel.eval(mel_import_command)

		# Rename back the .mtl
		if os.path.isfile(mtl_file_123):
			os.rename(mtl_file_123, mtl_file)
	else:
		# <The Main Obj Import>
		mel.eval(mel_import_command)

	# Select imported obj
	scene_transforms_after = cmds.ls(et='transform', long=True)
	imported_objs = list(set(scene_transforms_after) - set(scene_transforms_before))
	imported_objs.sort()

	cmds.select(imported_objs)

def export_obj(outputFilename, outputDir, ignoreMat=True):
	load_plugin('objExport.mll')
	
	selections = cmds.ls(sl=True, objectsOnly=True)
	
	if not selections:
		cmds.confirmDialog(title='Error', message='Nothing selected!')
		return

	for obj in selections:
		if not is_selection_geometry_node(obj):
			cmds.confirmDialog(title='Error', message=f'Non geometry detected in selection: `{obj}`')
			return

	output_file = os.path.join(outputDir, outputFilename)
	cmds.file(output_file, pr=True, typ='OBJexport', es=1, force=True, \
		op="groups=1;ptgroups=1;materials={0};smoothing=1;normals=1".format(int(not ignoreMat)))
	
	# Delete .mtl (never need it)
	output_file_mtl = output_file.replace('.obj', '.mtl')
	if os.path.isfile(output_file_mtl):
		os.remove(output_file_mtl)

	sys.stdout.write("Exported '{}'' to '{}'".format(selections, output_file))

def import_clipboard_obj(obj_file_path=None):
	if not obj_file_path:
		obj_file_path = CLIPBOARD_OBJ
	else:
		if isinstance(obj_file_path, str):
			if os.path.exists(obj_file_path):
				obj_file_path = Path(obj_file_path)
	
	selection = cmds.ls(sl=True, objectsOnly=True)
	group_to_parent = None
	# If it's a group
	if selection and len(selection) == 1 and cmds.nodeType(selection[0]) == 'transform' and not cmds.listRelatives(selection[0], shapes=True):
		group_to_parent = selection[0]

	top_level_transforms_before = cmds.ls(assemblies=True)
	if obj_file_path and obj_file_path.is_file():
		import_obj(obj_file_path)
		top_level_transforms_after = cmds.ls(assemblies=True)
		imported = set(top_level_transforms_after) - set(top_level_transforms_before)
		imported_renamed = []
		for obj in imported:
			new_name = re.sub(f"^{re.escape('import_')}", '', obj)
			renamed = cmds.rename(obj, new_name)
			if group_to_parent:
				renamed = cmds.parent(renamed, group_to_parent)[0]
			imported_renamed.append(renamed)
		cmds.select(imported_renamed, replace=True)

def export_clipboard_obj(obj_file_path=None):
	if not obj_file_path:
		obj_file_path = CLIPBOARD_OBJ
	else:
		if isinstance(obj_file_path, str):
			if os.path.exists(obj_file_path):
				obj_file_path = Path(obj_file_path)

	if obj_file_path:
		clipboard_dir = obj_file_path.parent
		if clipboard_dir.is_dir():
			export_obj('ClipboardObj.obj', clipboard_dir)

def import_nvil_obj():
	obj_file_path = None
	clipboard_text = QtWidgets.QApplication.clipboard().text()
	if os.path.exists(clipboard_text):
		obj_file_path = Path(clipboard_text)
	else:
		obj_file_path = NVIL_CLIPBOARD_OBJ
	import_clipboard_obj(obj_file_path)

def export_nvil_obj():
	export_clipboard_obj(NVIL_CLIPBOARD_OBJ)
	clipboard = QtWidgets.QApplication.clipboard()
	clipboard.setText('C:\\Users\\<USER>\\AppData\\Roaming\\DigitalFossils\\NVil\\Media\\Clipboard\\ClipboardObj.obj')
