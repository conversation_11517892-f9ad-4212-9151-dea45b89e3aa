import os

import maya.cmds as cmds

from PySide6.QtWidgets import QStyledItemDelegate, QStyle
from PySide6.QtCore import Qt, QRect, QEvent, QModelIndex, QSize
from PySide6.QtGui import QIcon, Q<PERSON>ainter

# Get the directory where this file is located
THIS_FILE_DIR = os.path.dirname(os.path.abspath(__file__))


class LayerVisibilityDelegate(QStyledItemDelegate):
	def __init__(self, parent=None):
		super().__init__(parent)
		# Use system icons if available
		icon_path = os.path.join(THIS_FILE_DIR, "icons")

		self.eye_open_icon = QIcon.fromTheme(
			"visibility", QIcon(os.path.join(icon_path, "eye_open.png"))
		)
		self.eye_closed_icon = QIcon.fromTheme(
			"visibility-off", QIcon(os.path.join(icon_path, "eye_closed.png"))
		)
		self.icon_size = 16
		self.drag_active = False
		self.last_drag_value = None

	def paint(self, painter, option, index):
		"""Paint the visibility icon based on the value"""
		if index.column() == 0:  # Visibility column
			# Get the item's visibility state
			visible = index.data(Qt.DisplayRole)

			# If visible is None or not set, try to determine from the target value
			if visible is None:
				# Get the row and find the target name
				row = index.row()
				model = index.model()
				if model:
					target_name = model.data(model.index(row, 1))

					# Get the blendshape node from the parent window
					blendshape_node = None
					parent_window = self.parent()
					while parent_window and not hasattr(parent_window, "ui"):
						parent_window = parent_window.parent()

					if (
						parent_window
						and hasattr(parent_window, "ui")
						and hasattr(parent_window.ui, "blendshape_nodes_list")
					):
						selected_items = parent_window.ui.blendshape_nodes_list.selectedItems()
						if selected_items:
							blendshape_node = selected_items[0].text()

					# Check the actual value in Maya
					if blendshape_node and target_name:
						try:
							weight = cmds.getAttr(f"{blendshape_node}.{target_name}")
							visible = weight > 0.0
							# Update the model with this value
							model.setData(index, visible)
						except:
							visible = False
					else:
						visible = False

			# Fill background based on selection state
			if option.state & QStyle.State_Selected:
				painter.fillRect(option.rect, option.palette.highlight())

			# Draw the appropriate icon centered in the cell
			icon = self.eye_open_icon if visible else self.eye_closed_icon
			icon_rect = QRect(
				option.rect.x() + (option.rect.width() - self.icon_size) // 2,
				option.rect.y() + (option.rect.height() - self.icon_size) // 2,
				self.icon_size,
				self.icon_size,
			)
			icon.paint(painter, icon_rect)
		else:
			# For other columns, use the default painting
			super().paint(painter, option, index)

	def editorEvent(self, event, model, option, index):
		if index.column() != 0:  # Only handle events for the visibility column
			return False

		if event.type() == QEvent.MouseButtonPress:
			# Start dragging
			self.drag_active = True

			# Toggle the current value
			current_visible = index.data(Qt.DisplayRole)
			self.last_drag_value = not current_visible

			# Update the current item's visibility
			model.setData(index, self.last_drag_value)

			# Get the table widget
			view = option.widget

			# Toggle the weight value between 0 and 1
			row = index.row()
			target_name = model.data(model.index(row, 1))  # Get target name from column 1

			# Get the blendshape node from the parent window
			blendshape_node = None
			parent_window = self.parent()
			while parent_window and not hasattr(parent_window, "ui"):
				parent_window = parent_window.parent()

			if (
				parent_window
				and hasattr(parent_window, "ui")
				and hasattr(parent_window.ui, "blendshape_nodes_list")
			):
				selected_items = parent_window.ui.blendshape_nodes_list.selectedItems()
				if selected_items:
					blendshape_node = selected_items[0].text()

			# Update both the slider and the actual blendshape attribute
			if hasattr(view, "cellWidget") and target_name:
				# Get the weight slider in column 2
				weight_slider = view.cellWidget(row, 2)
				if weight_slider:
					# Set weight to 1 if visible, 0 if not visible
					new_weight = 1.0 if self.last_drag_value else 0.0

					# Try to find the actual slider widget if it's a custom widget
					slider_to_update = weight_slider
					if hasattr(weight_slider, "sld"):
						slider_to_update = weight_slider.sld

					# Update the Maya blendshape attribute directly if we have a blendshape node
					if blendshape_node:
						cmds.setAttr(f"{blendshape_node}.{target_name}", new_weight)

					# Update the slider UI
					if hasattr(weight_slider, "setValue"):
						weight_slider.blockSignals(True)
						weight_slider.setValue(new_weight)
						weight_slider.blockSignals(False)
					elif hasattr(slider_to_update, "setValue"):
						slider_to_update.blockSignals(True)
						slider_to_update.setValue(new_weight)
						slider_to_update.blockSignals(False)
			else:
				print("Could not find weight slider or missing target")

			return True

		elif event.type() == QEvent.MouseMove and self.drag_active:
			# Get the item at the current mouse position
			view = option.widget
			# Use position() instead of deprecated pos()
			drag_index = view.indexAt(event.position().toPoint())

			# If we're over a valid item in the visibility column, update it
			if drag_index.isValid() and drag_index.column() == 0:
				current_value = drag_index.data(Qt.DisplayRole)
				if current_value != self.last_drag_value:
					model.setData(drag_index, self.last_drag_value)

					# Get target name and blendshape node
					row = drag_index.row()
					target_name = model.data(model.index(row, 1))

					# Get the blendshape node from the parent window
					blendshape_node = None
					parent_window = self.parent()
					while parent_window and not hasattr(parent_window, "ui"):
						parent_window = parent_window.parent()

					if (
						parent_window
						and hasattr(parent_window, "ui")
						and hasattr(parent_window.ui, "blendshape_nodes_list")
					):
						selected_items = parent_window.ui.blendshape_nodes_list.selectedItems()
						if selected_items:
							blendshape_node = selected_items[0].text()

					# Update both the slider and the actual blendshape attribute
					if hasattr(view, "cellWidget") and target_name:
						weight_slider = view.cellWidget(row, 2)
						if weight_slider:
							new_weight = 1.0 if self.last_drag_value else 0.0

							# Try to find the actual slider widget if it's a custom widget
							slider_to_update = weight_slider
							if hasattr(weight_slider, "sld"):
								slider_to_update = weight_slider.sld

							# Update the Maya blendshape attribute directly if we have a blendshape node
							if blendshape_node:
								cmds.setAttr(f"{blendshape_node}.{target_name}", new_weight)

							# Update the slider UI
							try:
								if hasattr(weight_slider, "setValue"):
									weight_slider.blockSignals(True)
									weight_slider.setValue(new_weight)
									weight_slider.blockSignals(False)
								elif hasattr(slider_to_update, "setValue"):
									slider_to_update.blockSignals(True)
									slider_to_update.setValue(new_weight)
									slider_to_update.blockSignals(False)
							except Exception as e:
								print(f"Error updating slider: {e}")
			return True

		elif event.type() == QEvent.MouseButtonRelease:
			# End drag operation
			self.drag_active = False
			return True

		return super().editorEvent(event, model, option, index)

	def sizeHint(self, option, index):
		size = super().sizeHint(option, index)
		if index.column() == 0:  # Make the visibility column just wide enough for the icon
			size.setWidth(self.icon_size + 12)  # Add some padding
		return size
