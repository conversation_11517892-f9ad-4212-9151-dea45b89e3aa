import os

import pymel.core as pm
import maya.cmds as cmds

from .utils import pyToAttr

def copy_material():
	"""
	Copy materials from the first selected object to the rest of the selected objects
	"""
	sels = cmds.ls(sl=True)
	if len(sels) >= 2:
		shader = cmds.listSets(type=1, extendToShape=True, object=sels[0])
		cmds.sets(sels[1:], e=True, forceElement=shader[0])

def create_uv_checker():
	if not cmds.objExists('UV_Checker'):
		shader = pm.shadingNode('blinn', asShader=True, n='UV_Checker')
		shader.diffuse.set(1)
		shader.reflectivity.set(0)
		shader.specularRollOff.set(0)
		shader.eccentricity.set(0)
		shader.specularColor.set((0.0,0.0,0.0))
		
		# Create file texture node and make neccessary connections
		filenode = pm.mel.eval('createRenderNodeCB -as2DTexture "" file "";')
		filenode = pm.PyNode(filenode)
		pm.rename(filenode, 'UV_Checker_color_map')
		
		THIS_FILE_DIR = os.path.split(os.path.realpath(__file__))[0]
		cmds.setAttr("UV_Checker_color_map.fileTextureName", "%s/assets/textures/checker.bmp" % THIS_FILE_DIR, type="string")

		cmds.connectAttr("UV_Checker_color_map.outColor", "UV_Checker.color", f=True)

def apply_uv_checker():
	sels = pm.selected()
	
	for obj in sels:
		itsShadingGroup = pm.listConnections( obj.getShape(), type='shadingEngine' )
		itsShader = pm.ls( pm.listConnections(itsShadingGroup), materials=True )[0]
		
		cmds.select( cl=True )
		
		if itsShader != 'UV_Checker':
			nodeAttr = obj.longName() + '.shader_name'
			pyToAttr( nodeAttr, itsShader )
			create_uv_checker()
			pm.select( obj )
			pm.hyperShade( assign='UV_Checker' )
		else:
			# Select the place2dTexture node
			fileNode = pm.PyNode('UV_Checker_color_map')
			itsPlace2dTextureNode = pm.ls( pm.listConnections(fileNode), exactType='place2dTexture' )
			if len(itsPlace2dTextureNode) > 0:
				pm.select(itsPlace2dTextureNode[0])
				return True
	
	pm.select( sels )

def restoreShader():
	sels = pm.selected()
	
	for obj in sels:
		itsShadingGroup = pm.listConnections( obj.getShape(), type='shadingEngine' )
		itsShader = pm.ls( pm.listConnections(itsShadingGroup), materials=True )[0]
		
		if itsShader == 'UV_Checker':
			cmds.select( cl=True )
			previous_shader = attrToPy( obj.longName() + '.shader_name' )
			pm.select( obj )
			pm.hyperShade( assign=previous_shader )

	pm.select( sels )

