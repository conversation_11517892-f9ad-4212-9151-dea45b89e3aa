# -*- coding: utf-8 -*-
"""
Maya Python script to list the scene hierarchy focusing only on transform nodes.
Provides visual styling using text characters in the Maya Script Editor output.
Compatible with Maya 2025 (Python 3).
Does not create any GUI elements.
"""

import maya.cmds as cmds
import sys

# --- Configuration for visual style ---
# Ensures these characters print correctly in Maya 2025 Script Editor (UTF-8)
PREFIX_BRANCH = "│   "  # Vertical line for ongoing branches |
PREFIX_TEE    = "├─ "    # Connector for items with siblings below T
PREFIX_CORNER = "└─ "   # Connector for the last item in a branch L
PREFIX_SPACE  = "    "  # Space for levels under a last item (4 spaces)
# --- End Configuration ---

def print_hierarchy_recursive(node_path, prefix, is_last_sibling):
    """
    Recursively prints the hierarchy for a given node and its children.

    Args:
        node_path (str): The full path to the Maya transform node (e.g., '|group1|pCube1').
        prefix (str): The string prefix built from ancestor hierarchy symbols (e.g., "│   ").
        is_last_sibling (bool): True if this node is the last among its direct siblings.
    """
    # 1. Determine the connector type based on whether it's the last sibling
    connector = PREFIX_CORNER if is_last_sibling else PREFIX_TEE

    # 2. Get the short name for display purposes
    # Splitting by '|' and taking the last element gets the node's direct name
    node_name = node_path.split('|')[-1]
    if not node_name: # Should not happen with valid full paths, but as a safeguard
        node_name = node_path

    # 3. Print the current node line with its prefix and connector
    try:
        # Use sys.stdout.write for potentially better handling of unicode in some environments
        # Maya 2025 Script Editor generally handles print() with UTF-8 fine
        print(f"{prefix}{connector}{node_name}")
    except UnicodeEncodeError:
        # Fallback if encoding issues occur (less likely in Maya 2025 Python 3)
        print(prefix + connector.encode('utf-8', 'replace').decode() + node_name.encode('utf-8', 'replace').decode())


    # 4. Prepare the prefix string for the children of *this* node
    # If this node was the *last* sibling, the vertical line ('│') stops at this level,
    # so its children get spaces instead of a vertical line in their prefix contribution.
    child_prefix = prefix + (PREFIX_SPACE if is_last_sibling else PREFIX_BRANCH)

    # 5. Find *transform* children only for the current node
    # Using fullPath=True ensures we get unique paths, essential for complex scenes
    children = cmds.listRelatives(node_path, children=True, type='transform', fullPath=True) or []
    # The 'or []' handles the case where listRelatives returns None instead of an empty list

    # 6. Recursively call this function for each child transform
    num_children = len(children)
    for i, child_path in enumerate(children):
        # Determine if the current child is the last one in the list of children
        is_last_child = (i == num_children - 1)
        # Recursive call for the child node
        print_hierarchy_recursive(child_path, child_prefix, is_last_child)


def list_scene_hierarchy_styled():
    """
    Main function to list the scene hierarchy transforms with visual styling.
    Prints the output directly to the Maya Script Editor history pane.
    """
    # Header for the output
    print("\n" + "=" * 60)
    print(" Scene Hierarchy (Transforms Only)")
    print("=" * 60)

    # Get all top-level nodes in the scene (nodes directly under the world)
    # Using long=True provides the full path, which is more robust than short names
    top_level_nodes = cmds.ls(assemblies=True, long=True)

    # Check if there are any top-level nodes at all
    if not top_level_nodes:
        print("[Scene is empty or has no top-level objects]")
        print("=" * 60 + "\n")
        return

    # Filter the top-level nodes to ensure we only process actual transforms
    # `ls(assemblies=True)` can sometimes include default cameras if they are unparented.
    top_level_transforms = [node for node in top_level_nodes if cmds.nodeType(node) == 'transform']

    # Check if there are any top-level *transforms* after filtering
    if not top_level_transforms:
        print("[No top-level transforms found (only shapes or other node types?)]")
        print("=" * 60 + "\n")
        return

    # Iterate through each top-level transform node
    num_top_level = len(top_level_transforms)
    for i, node_path in enumerate(top_level_transforms):
        # Get the short name for the top-level node for display
        node_name = node_path.split('|')[-1]
        if not node_name:
             node_name = node_path

        # Print the top-level node name directly. It doesn't have a prefix or connector.
        print(node_name)

        # Find the direct *transform* children of this top-level node
        children = cmds.listRelatives(node_path, children=True, type='transform', fullPath=True) or []

        # Iterate through the children and start the recursive printing process for each one
        num_children = len(children)
        for j, child_path in enumerate(children):
            # Determine if this child is the last among the direct children of the top-level node
            is_last_child = (j == num_children - 1)
            # Initiate the recursive printing. The initial prefix for the first level children is empty ("").
            print_hierarchy_recursive(child_path, "", is_last_child)

        # Optional: Add a blank line separator between different top-level hierarchies for clarity
        # if i < num_top_level - 1:
        #     print() # Adds a visual break

    # Footer for the output
    print("=" * 60 + "\n")

list_scene_hierarchy_styled()