import os

import maya.cmds as cmds
import pymel.core as pm

from mgear.core import skin

from .utils import obj_is_type, timing

def safe_bind():
	"""
	Check mesh before bindskin and also rename skincluster
	
	Other Features:
	 * Can select multiple meshes to bind in one step
	 * Selection Order of joint and mesh doesn't matter
	"""
	selections = cmds.ls(sl=True)

	joints = [i for i in selections if cmds.nodeType(i) == 'joint']
	meshes = [i for i in selections if obj_is_type(i, 'mesh')]

	def is_transformation_freezed(o):
		if cmds.getAttr(f"{o}.translateX") == 0.0 and \
			cmds.getAttr(f"{o}.translateY") == 0.0 and \
			cmds.getAttr(f"{o}.translateZ") == 0.0 and \
			cmds.getAttr(f"{o}.rotateX") == 0.0 and \
			cmds.getAttr(f"{o}.rotateY") == 0.0 and \
			cmds.getAttr(f"{o}.rotateZ") == 0.0 and \
			cmds.getAttr(f"{o}.scaleX") == 1.0 and \
			cmds.getAttr(f"{o}.scaleY") == 1.0 and \
			cmds.getAttr(f"{o}.scaleZ") == 1.0:
			return True
		else:
			return False

	warning_meshes = []

	for m in meshes:
		# warns if mesh has transformations
		if not(is_transformation_freezed(m)):
			warning_message = "{} has non-zero transformations.".format(m)
			cmds.confirmDialog(title="WARNING", message=warning_message)
			print("WARNING: " + warning_message)
			warning_meshes.append(m)
		
		# check history
		history = []
		history_nodes = cmds.listHistory(m, pruneDagObjects=True) or []
		for i in history_nodes:
			# filter out some types
			node_type = cmds.nodeType(i)
			if node_type == 'skinCluster':
				cmds.error("`{}` is already connected to a skinCluster".format(i))
			if node_type not in ['groupId', 'shadingEngine', 'objectSet']:
				history.append(i)
		
		if history:
			warning_message = "{} has history: {}".format(m, history)
			cmds.confirmDialog(title="WARNING", message=warning_message)
			print("WARNING: " + warning_message)
			warning_meshes.append(m)

		# check for intermediate shapes
		intermediate_shapes = []
		shapes = cmds.listRelatives(m, shapes=True, fullPath=True) or []
		for each_child in shapes:
			if cmds.getAttr(f"{each_child}.intermediateObject"):
				intermediate_shapes.append(each_child)
		
		if intermediate_shapes:
			error_message = "{} has {} intermediate shapes".format(m, len(intermediate_shapes))
			cmds.confirmDialog(title="ERROR", message=error_message)
			#cmds.error("ERROR: " + error_message)
			warning_meshes.append(m)

		kwargs = {
			'name': m.split('|')[-1] + '_SkinCluster',
			'toSelectedBones': True,
			'bindMethod': 0,  # Closest Distance
			'skinMethod': 0,  # Classic linear
			'normalizeWeights': 1,  # Interactive
			'weightDistribution': 1,  # Neighbours
			'maximumInfluences': 4,  # Max Influences
			'obeyMaxInfluences': False,  # Maintain max influence
			'removeUnusedInfluence': False,  # Remove unused influences
			'includeHiddenSelections': True  # Include hidden selections on creation
		}
		skin = cmds.skinCluster(joints, m, **kwargs)
		print("BIND SUCCESS: " + m)
	
	cmds.select(clear=True)

	if warning_meshes:
		cmds.select(warning_meshes)

def delete_intermediate_shapes():
	"""
	Delete all Intermediate Shapes from selected meshes
	"""
	selections = cmds.ls(sl=True)

	for obj in selections:
		shapes = cmds.listRelatives(obj, shapes=True, fullPath=True) or []
		for shape in shapes:
			if cmds.getAttr(f"{shape}.intermediateObject"):
				cmds.delete(shape)

# _____________________________________ JOINTS  _____________________________________

def create_joint_orientation_debug_cylinders():
	"""
	Creates orientation cylinders for selected joints:
	- Blue cylinder oriented along Z-axis
	- Green cylinder oriented along Y-axis
	"""

	# Get selected joints
	selected_joints = cmds.ls(selection=True, type="joint")

	if not selected_joints:
		cmds.warning("No joints selected. Please select one or more joints.")
		return

	# Create or get materials
	blue_material = create_or_get_material("blue_orientation_blinn", [0.2, 0.4, 0.8])
	green_material = create_or_get_material("green_orientation_blinn", [0.2, 0.8, 0.4])

	created_objects = []

	for joint in selected_joints:
		# Clean joint name for naming convention
		joint_clean_name = joint.replace(":", "_")

		# Get joint position
		joint_position = cmds.xform(joint, query=True, translation=True, worldSpace=True)

		# Create Z-axis cylinder (blue)
		z_cylinder_transform, z_cylinder_shape = cmds.polyCylinder(
			name=f"{joint_clean_name}_z_orientation_cyl",
			radius=1.0,
			height=2.0,
			subdivisionsX=8,
			subdivisionsY=1,
			subdivisionsZ=1,
			constructionHistory=True,
		)

		# Position at joint location
		cmds.xform(z_cylinder_transform, translation=joint_position, worldSpace=True)

		# Parent to joint
		cmds.parent(z_cylinder_transform, joint)

		# Scale Z-axis cylinder
		cmds.setAttr(f"{z_cylinder_transform}.scaleX", 0.16)
		cmds.setAttr(f"{z_cylinder_transform}.scaleY", 4.5)
		cmds.setAttr(f"{z_cylinder_transform}.scaleZ", 0.16)
		cmds.setAttr(f"{z_cylinder_transform}.rotateX", 90.0)
		cmds.setAttr(f"{z_cylinder_transform}.rotateY", 0.0)
		cmds.setAttr(f"{z_cylinder_transform}.rotateZ", 0.0)

		# Reset transforms after parenting
		# cmds.makeIdentity(z_cylinder_transform, apply=True, translate=True)

		# Assign blue material
		assign_material(z_cylinder_transform, blue_material)

		# Create Y-axis cylinder (green)
		y_cylinder_transform, y_cylinder_shape = cmds.polyCylinder(
			name=f"{joint_clean_name}_y_orientation_cyl",
			radius=1.0,
			height=2.0,
			subdivisionsX=8,
			subdivisionsY=1,
			subdivisionsZ=1,
			constructionHistory=True,
		)

		# Position at joint location
		cmds.xform(y_cylinder_transform, translation=joint_position, worldSpace=True)

		# Parent to joint
		cmds.parent(y_cylinder_transform, joint)

		# Scale Y-axis cylinder
		cmds.setAttr(f"{y_cylinder_transform}.scaleX", 0.16)
		cmds.setAttr(f"{y_cylinder_transform}.scaleY", 4.5)
		cmds.setAttr(f"{y_cylinder_transform}.scaleZ", 0.16)
		cmds.setAttr(f"{y_cylinder_transform}.rotateX", 0.0)
		cmds.setAttr(f"{y_cylinder_transform}.rotateY", 0.0)
		cmds.setAttr(f"{y_cylinder_transform}.rotateZ", 0.0)

		# Reset transforms after parenting
		# cmds.makeIdentity(y_cylinder_transform, apply=True, translate=True)

		# Assign green material
		assign_material(y_cylinder_transform, green_material)

		created_objects.extend([z_cylinder_transform, y_cylinder_transform])

		print(f"Created orientation cylinders for joint: {joint}")

def create_or_get_material(material_name, color):
	"""
	Creates a new Blinn material or returns existing one

	Args:
	    material_name (str): Name of the material
	    color (list): RGB color values [r, g, b]

	Returns:
	    str: Material name
	"""
	# Check if material already exists
	if cmds.objExists(material_name):
		print(f"Using existing material: {material_name}")
		return material_name

	# Create new Blinn material
	material = cmds.shadingNode("blinn", asShader=True, name=material_name)

	# Set material color
	cmds.setAttr(f"{material}.color", color[0], color[1], color[2], type="double3")

	# Create shading group
	shading_group = cmds.sets(
		renderable=True, noSurfaceShader=True, empty=True, name=f"{material_name}_SG"
	)

	# Connect material to shading group
	cmds.connectAttr(f"{material}.outColor", f"{shading_group}.surfaceShader", force=True)

	print(f"Created new material: {material_name}")
	return material


def assign_material(geometry, material):
	"""
	Assigns material to geometry

	Args:
	    geometry (str): Name of the geometry
	    material (str): Name of the material
	"""
	# Get the shading group
	shading_group = f"{material}_SG"

	# Assign material
	cmds.sets(geometry, edit=True, forceElement=shading_group)

def cleanup_orientation_cylinders():
	"""
	Removes all orientation cylinders from the scene
	"""
	# Find all orientation cylinders
	all_transforms = cmds.ls(type="transform")
	orientation_cylinders = [
		obj for obj in all_transforms if "_orientation_cyl" in obj and cmds.objExists(obj)
	]

	if orientation_cylinders:
		cmds.delete(orientation_cylinders)
		print(f"Deleted {len(orientation_cylinders)} orientation cylinders.")
	else:
		print("No orientation cylinders found to delete.")


# _____________________________________ MGEAR  _____________________________________

@timing
def mgear_skin_import():
	selection = pm.selected()
	for geo in selection:
		pm.select(geo, replace=True)
		if geo.hasAttr("rabbitSkinPath"):
			skin_path = geo.rabbitSkinPath.get()
			print(skin_path)
			skin.importSkin(skin_path)
	pm.select(selection, replace=True)

@timing
def mgear_skin_export():
	selection = pm.selected()
	for geo in selection:
		pm.select(geo, replace=True)
		if geo.hasAttr("rabbitSkinPath"):
			skin_path = geo.rabbitSkinPath.get()
			print(skin_path)
			skin.exportSkin(skin_path)
	pm.select(selection, replace=True)

def add_skin_path_attr():
	selection = pm.selected()

	skin_dir_path = None

	filerules = cmds.workspace(fileRule=True, q=True)
	filerules = dict(zip(filerules[::2], filerules[1::2]))
	if "skin" in filerules.keys():
		project_path = cmds.workspace(query=1, rootDirectory=1)
		skin_dir_path = filerules["skin"]
		skin_dir_path = os.path.join(project_path, skin_dir_path)

	for i in selection:
		if not i.hasAttr("rabbitSkinPath"):
			i.addAttr("rabbitSkinPath", dataType='string')
			if skin_dir_path:
				skin_path = os.path.join(skin_dir_path, i.nodeName() + '.gSkin').replace("\\", "/")
				i.setAttr("rabbitSkinPath", skin_path)
