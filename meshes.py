from math import isclose

import maya.mel as mel
import pymel.core as pm
import maya.cmds as cmds

import maya.api.OpenMaya as om

from .utils import timing, obj_is_type, UndoStack, load_plugin

maya_useNewAPI = True

@timing
def quick_separate():
	selections = cmds.ls(sl=True)
	for selection in selections:
		if obj_is_type(selection, 'mesh'):
			cmds.polySeparate(selections, ch=False)

def select_hard_edges():
	hard_edges = []
	m_selections = om.MGlobal.getActiveSelectionList()  # returns MSelectionList
	for i in range(m_selections.length()):
		dag_path = m_selections.getDagPath(i)  # returns MDagPath
		# dag_path.extendToShape()  # if you want to get the shape's Dag Path
		if dag_path.hasFn(om.MFn.kMesh):  # check if it's compatible
			mesh_fn = om.MFnMesh(dag_path)
			num_of_edges = mesh_fn.numEdges

			for j in range(num_of_edges):
				if not mesh_fn.isEdgeSmooth(j):
					hard_edges.append(f"{dag_path.fullPathName()}.e[{j}]") # getSelectionStrings() returns tuple ('name', )

	if hard_edges:
		cmds.select(hard_edges)
	else:
		cmds.select(cl=True)

def toogle_mesh_selection():
	"""
	Credit: JimBo Drew
	(http://forum.mgear-framework.com/t/selection-mask-toggle/1936/5)
	"""
	# Test if ANY of these 3 flags are on or not.
	areSurfacesOn = [
			pm.selectType(polymesh=True, q=True),
			pm.selectType(nurbsSurface=True, q=True),
			pm.selectType(subdiv=True, q=True)
			]
	# Get the opposite value. If they are all True, it will be False.
	oppositeValue = not any(areSurfacesOn)
	pm.mel.setObjectPickMask("Surface", oppositeValue)

# _____________________________________ SYMMETRY  _____________________________________

def SHAPES_check_symmetry():
	if not cmds.pluginInfo('SHAPESTools.mll', q=True, loaded=True):
		cmds.error("Please load SHAPES first")
	mel.eval("br_polySymmetry  -axis x -direction 1 -tolerance 0.001")

def SHAPES_symmetrize():
	"""
	Note: Currently doesn't work on skinned mesh. Check SHAPES_mirror.mel: global proc shapesMirror_makeSymmetrical()
	"""
	if not cmds.pluginInfo('SHAPESTools.mll', q=True, loaded=True):
		cmds.error("Please load SHAPES first")
	selection = cmds.ls(sl=True, objectsOnly=True)
	if len(selection) != 1:
		cmds.error("Please select only one object")
	obj = selection[0]
	has_order_mapping = mel.eval(f'br_polyMapVertexOrder -check "{obj}"')
	if has_order_mapping < 0:
		mel.eval('shapesMain_getMeshSelection 1;')  # 1 == skip dialog
	shape = mel.eval("shapesMain_getShapeNode")
	mel.eval(f'br_polySymmetry -axis x -direction 1 -applyOrderMap "{shape}";')

class PreserveSymmetry(object):
	"""Context manager to preserve Maya's symmetry modeling state.

	Example:
	    with PreserveSymmetry():
	        cmds.symmetricModelling(symmetry=False)  # Temporarily disable symmetry
	        # Do asymmetric operations...
	    # Original symmetry state is restored automatically
	"""

	def __init__(self, restore=True, symmetry=None, about=None, axis=None, tolerance=None):
		"""
		Args:
		    restore (bool): If True, restore original settings on exit. If False, leave modified.
		    symmetry (bool, optional): Override symmetry state (instead of querying current).
		    axis (str, optional): Override symmetry axis (instead of querying current).
		    tolerance (float, optional): Override tolerance (instead of querying current).
		"""
		self.restore = restore
		self.overrides = {"symmetry": symmetry, "about": about, "axis": axis, "tolerance": tolerance}

	def __enter__(self):
		"""Store current symmetry settings when entering the context."""
		self.original_state = {
			"symmetry": cmds.symmetricModelling(query=True, symmetry=True),
			"about": cmds.symmetricModelling(query=True, about=True),
			"axis": cmds.symmetricModelling(query=True, axis=True),
			"tolerance": cmds.symmetricModelling(query=True, tolerance=True),
		}

		# Apply overrides if provided
		for key, value in self.overrides.items():
			if value is not None:
				cmds.symmetricModelling(**{key: value})

		return self  # Optional: return self to allow access to original_state

	def __exit__(self, exc_type, exc_val, exc_tb):
		"""Restore original settings when exiting the context."""
		if self.restore:
			cmds.symmetricModelling(
				edit=True,
				symmetry=self.original_state["symmetry"],
				about=self.original_state["about"],
				axis=self.original_state["axis"],
				tolerance=self.original_state["tolerance"],
			)
		return False  # Propagate exceptions if they occurred

def symmetrize_left_to_right():
	with UndoStack():
		cmds.symmetricModelling(symmetry=True, about='object', axis='x')
		symmetric_selection()
		mel.eval('Symmetrize')

def symmetric_selection(selection_type='left'):
	# Select All Verts in the mesh
	mel.eval('selectMode -component; SelectVertexMask;')
	mel.eval('SelectAll;')
	symmetry_mode = cmds.symmetricModelling(symmetry=True, q=True)
	cmds.symmetricModelling(symmetry=True, e=True)
	
	if selection_type in ['left', 'l']:
		# Select Mesh Left
		cmds.select(pm.filterExpand(ex=True, sm=31, symNegative=True))
	elif selection_type in ['right', 'r']:
		# Select Mesh Right
		cmds.select(cmds.filterExpand(ex=True, sm=31, symPositive=True))
	elif selection_type in ['center', 'middle', 'm']:
		# Select Mesh Center
		cmds.select(cmds.filterExpand(ex=True, sm=31, symSeam=True))

	cmds.symmetricModelling(symmetry=symmetry_mode, e=True) # Restore Symmetry mode

def get_symmetry_table(meshObj, returnIndexes=True):
	"""
	TODO: support provided edge from parameter
	"""
	saved_symmetry_mode = cmds.symmetricModelling(q=True, symmetry=True)
	saved_selections = cmds.ls(sl=True)

	def restore_symmetry_and_selections():
		nonlocal saved_symmetry_mode
		nonlocal saved_selections
		cmds.symmetricModelling(symmetry=saved_symmetry_mode, e=True)
		cmds.select(saved_selections, replace=True)

	cmds.select(meshObj, replace=True)

	# mel.eval('selectMode -component; SelectEdgeMask;')
	# mel.eval('SelectAll;')
	
	# Get Center Edges
	source_obj = meshObj
	cmds.symmetricModelling(symmetry=True, e=True, topoSymmetry=False)
	cmds.symmetricModelling(symmetry=True, e=True, axis='x')
	edge_count = cmds.polyEvaluate(source_obj, edge=True)
	center_edges = cmds.filterExpand(f'{source_obj}.e[0:{edge_count-1}]', ex=True, sm=32, symSeam=True)
	
	center_edge= None
	if center_edges:
		for edge in center_edges:
			vertices = cmds.ls(cmds.polyListComponentConversion(edge, fromEdge=True, toVertex=True), flatten=True)
			vert0_posX = cmds.xform(vertices[0], query=True, translation=True, objectSpace=True)[0]
			vert1_posX = cmds.xform(vertices[1], query=True, translation=True, objectSpace=True)[0]
			if isclose(vert0_posX, 0.0, abs_tol=0.0001) and isclose(vert1_posX, 0.0, abs_tol=0.0001):
				center_edge = edge
				break
	
	if not center_edge:
		cmds.symmetricModelling(symmetry=True, e=True, topoSymmetry=False)
		cmds.symmetricModelling(symmetry=True, e=True, axis='x', about="world")
	else:
		cmds.symmetricModelling(center_edge, symmetry=True, e=True, topoSymmetry=True)

	vert_count = cmds.polyEvaluate(source_obj, vertex=True)
	left_vertices = cmds.filterExpand(f'{source_obj}.vtx[0:{vert_count-1}]', ex=True, sm=31, symNegative=True)

	# sym_table_component_strings = {}  # eg. {'pSphere3.vtx[5]': 'pSphere3.vtx[3]', ...}
	sym_table_indexes = {}  # e.g. {'5': '3', ...}

	def component_string_to_index(compStr):
		return int(compStr.split('[')[-1][:-1])

	for left_vert in left_vertices:
		cmds.select(left_vert, symmetry=True, replace=True)
		verts_pair = cmds.ls(sl=True, flatten=True)
		
		left_index = component_string_to_index(left_vert)
		if len(verts_pair) != 2:
			sym_table_indexes[left_index] = left_index  # opposite component not found
		right_vert = verts_pair[1] if left_vert == verts_pair[0] else verts_pair[0]
		# sym_table_component_strings[left_vert] = right_vert
		right_index = component_string_to_index(right_vert)
		sym_table_indexes[left_index] = right_index
		sym_table_indexes[right_index] = left_index
	
	sym_table_indexes_keys = sym_table_indexes.keys()
	for i in range(vert_count):
		if i not in sym_table_indexes_keys:
			sym_table_indexes[i] = i
	
	restore_symmetry_and_selections()

	if returnIndexes:
		return(sym_table_indexes)
	else:
		# return(sym_table_component_strings)
		pass
