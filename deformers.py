"""
TODO:
	'blendShape', 
	'cluster', 
	'deformBend', 
	'deformFlare', 
	'deformSine', 
	'deformSquash', 
	'deformTwist', 
	'deformWave', 
	'ffd', 
	'jiggle', 
	'nonLinear', 
	'sculpt', 
	'skinCluster', 
	'softMod', 
	'wire', 
	'wrap'
"""

from functools import partial

import maya.mel as mel
import pymel.core as pm
import maya.cmds as cmds

import maya.api.OpenMaya as om
import maya.api.OpenMayaAnim as oma

from .objects import duplicate_base
from .meshes import get_symmetry_table
from .tools.quickMenu import QuickMenu, open_quick_menu
from .utils import obj_is_type, is_selection_geometry_node, get_cluster_node, mirror_name, get_object_side

maya_useNewAPI = True

# _____________________________________ REPEAT DECORATOR  _____________________________________

_repeat_function = None
_args = None
_kwargs = None

def _repeat_command():
	if _repeat_function is not None:
		_repeat_function(*_args, **_kwargs)

def repeatable(function):
	def wrapper(*args, **kwargs):
		global _repeat_function
		global _args
		global _kwargs

		_repeat_function = function
		_args = args
		_kwargs = kwargs

		_repeat_command_str = 'python("%s._repeat_command()")' % __name__
		ret = function(*args, **kwargs)
		try:
			cmds.repeatLast(ac=_repeat_command_str, acl=function.__name__)
		except RuntimeError:
			pass 

		return ret
	return wrapper

# _____________________________________ UTILS  _____________________________________

def is_number(x):
	return isinstance(x, int) or isinstance(x, float)

def is_deformer_node(x):
		return 'geometryFilter' in cmds.nodeType(x, inherited=True)

def is_mesh_node(x):
	return 'mesh' in cmds.nodeType(x, inherited=True)

def remove_duplicates_from_list(seq):
    seen = set()
    seen_add = seen.add
    return [x for x in seq if not (x in seen or seen_add(x))]

def get_deformers_from_object(object_name, ignore_mesh=False) -> list:
	if not cmds.objExists(object_name):
		return None

	if is_deformer_node(object_name):
		return object_name
	
	shapes = []
	if cmds.nodeType(object_name) == 'transform':
		shapes = cmds.listRelatives(object_name, fullPath=True)
	else:
		shapes.append(object_name)
	
	nodes = []
	for shape in shapes:
		if ignore_mesh and is_mesh_node(shape):
			continue
		if is_mesh_node(shape):
			nodes1 = cmds.listHistory(shape, pruneDagObjects=True) or []
		else:
			nodes1 = cmds.listConnections(shape) or []
		if nodes1:
			nodes += nodes1
	
	nodes = remove_duplicates_from_list( [x for x in nodes if is_deformer_node(x)] )

	if not nodes:
		return None
	else:
		return nodes

def get_meshes_and_deformers(selections):
	"""
	Analyze selection and separate into deformers and mesh objects.
	Returns:
			tuple: (deformers, mesh_objects) - Two lists containing the categorized objects
	"""
	deformers = []
	mesh_objects = []
	for obj in selections:
		if cmds.nodeType(obj) == 'transform':
			shapes = cmds.listRelatives(obj, shapes=True, fullPath=True) or []
		else:
			shapes = [obj]
		for shape in shapes:
			if is_mesh_node(shape):
				mesh_objects.append(obj)
			elif is_deformer_node(shape):
				deformers.append(obj)
			break
	return deformers, mesh_objects

def smart_set_weight(weightVal):
	"""
	Set deformer weights for selected components.
	
	Usage:
	1. Select components (vertices, edges, faces) from one or multiple meshes
	2. Optionally select a cluster handle to target that specific deformer
	3. If no specific deformer is selected, the function will find all deformers 
	   affecting the selected components and set weights for all of them
	
	The function handles three scenarios:
	- Multiple selections with a cluster: Sets weight on that specific cluster
	- Multiple selections with deformers: Sets weight on all found deformers
	- Single selection: Finds all deformers affecting the selection and sets weights
	"""
	selections = cmds.ls(sl=True, objectsOnly=True)
	if len(selections) > 1:
		cluster_found = get_nth_shape_type(selections, 1, 'clusterHandle')
		if cluster_found:
			cmds.select(cluster_found, deselect=True)
			c = get_cluster_node(cluster_found)
			cmds.percent(c, v=weightVal)
		else:
			deformers, mesh_objects = get_meshes_and_deformers(selections)
			for d in deformers:
				cmds.percent(d, v=weightVal)
	else:
		deformers = set()
		for obj in selections:
			deformers.update(get_deformers_from_object(obj))
		cmds.select(deformers, deselect=True)
		for d in deformers:
			cmds.percent(d, v=weightVal)

def zero_weight():
	smart_set_weight(0.0)

def full_weight():
	smart_set_weight(1.0)

@repeatable
def add_to_deformer():
	set_deformer_membership('add')

@repeatable
def remove_from_deformer():
	set_deformer_membership('remove')

def set_deformer_membership(mode='add'):
	selections = cmds.ls(sl=True, flatten=True)
	components = []
	objects = []
	deformers = []
	for x in selections:
		if '.' in x:
			components.append(x)
		else:
			deformers_ = get_deformers_from_object(x, ignore_mesh=True)
			if deformers_:
				deformers += deformers_
			else:
				objects.append(x)
	
	# Cleanup duplicates
	objects = list(set(objects))
	deformers = list(set(deformers))
	
	# print("// set_deformer_membership():")
	# print(f"   components: {components}")
	# print(f"   objects: {objects}")
	# print(f"   deformers: {deformers}")
	
	# Mel References
	# deformer -edit -rm  -geometry pSphere2 ffd1
	# percent -v 0 cluster2 pSphere1.vtx[222:225] pSphere1.vtx[242:243] ;
	# percent -v 0 ffd1 pSphere1.vtx[246] pSphere1.vtx[255] pSphere1.vtx[262:265] ;

	if deformers and components:
		# components_str = ', '.join(components)
		value = 1 if mode == 'add' else 0
		for deformer in deformers:
			cmds.percent(deformer, components, value=value)

	for obj in objects:
		for deformer in deformers:
			if mode == 'add':
				cmds.deformer(deformer, e=True, geometry=obj)
			else:
				cmds.deformer(deformer, e=True, remove=True, geometry=obj)

def set_blendshape_weight_for_selected_verts(weight=0.0):
	"""
	Set Selected Vert's Blendshape Weight to zero
	
	TODO:
	Assume only one Blendshape node on the mesh
	Assume selections are vertices and the vertices are on the same mesh
	"""
	selections = pm.selected(flatten=True)
	mesh = selections[0].node()
	
	blendshape_list = []
	for mesh_set in mesh.listSets():
		if isinstance(mesh_set, pm.nodetypes.ObjectSet):
			for input in mesh_set.inputs():
				if isinstance(input, pm.nodetypes.BlendShape):
					blendshape_list.append(input)

	for blendshape_node in blendshape_list:
		for i in selections:
			if isinstance(i, pm.general.MeshVertex):
				cmds.setAttr('{0}.inputTarget[0].baseWeights[{1}]'.format(blendshape_node.longName(), i.index()), weight)

def set_deformers_envelope(*args, nodeType=None):
	"""
	set_deformers_envelope( 0.0 ) => work on selections
	set_deformers_envelope( object_name, 0.0 )
	set_deformers_envelope( [list of objects...], 0.0 )

	set_deformers_envelope( 0, nodeType='cluster' )
	set_deformers_envelope( 0, nodeType='blendShape' )
	"""
	
	unfiltered_list = []
	value = None
	if len(args) == 1 and is_number(args[0]):
		unfiltered_list = cmds.ls(sl=True)
		# unfiltered_list = cmds.ls(sl=True, dagObjects=True)
		value = args[0]
	elif len(args) == 2 and isinstance(args[0], str) and is_number(args[1]):
		unfiltered_list = [args[0]]
		value = args[1]
	elif len(args) == 2 and isinstance(args[0], list) and is_number(args[1]):
		unfiltered_list = args[0]
		value = args[1]
	
	print(f"unfiltered_list: {unfiltered_list}")
	
	if not unfiltered_list or value is None:
		# print(f"[!!!] set_deformers_envelope() FAILED: unfiltered_list:{unfiltered_list}, value:{value}")
		return None
	
	# get all shapes
	all_shapes = []
	for obj in unfiltered_list:
		if cmds.objExists(obj):
			if cmds.nodeType(obj) == 'transform':
				shapes = cmds.listRelatives(obj, fullPath=True)
				if shapes:
					all_shapes += shapes
			else:
				all_shapes.append(obj)
	
	print(f"all_shapes: {all_shapes}")
	
	# get connections/history from shapes
	all_nodes = []
	if all_shapes:
		for shape in all_shapes:
			if is_deformer_node(shape):
				all_nodes.append(shape)
				continue
			elif is_mesh_node(shape):
				# get only inputs if it's mesh
				nodes = cmds.listHistory(shape, pruneDagObjects=True)
			else:
				# otherwise get inputs and outputs (lattice, cluster, etc)
				nodes = cmds.listConnections(shape)
			if nodes:
				all_nodes += nodes
	
	print(f"all_nodes: {all_nodes}")
	
	if nodeType:
		all_nodes = set(cmds.ls(all_nodes, type=nodeType))
	
	failed_nodes = []
	for node in all_nodes:
		try:
			cmds.setAttr(f"{node}.envelope", value)
		except:
			failed_nodes.append(node)
	
	if failed_nodes:
		# print(f"[!!!] set_deformers_envelope() FAILED: to set .envelope value for the following nodes: '{', '.join(set(failed_nodes))}'")
		return None

# _____________________________________ CLUSTERS  _____________________________________

def create_cluster():
	selection = cmds.ls(sl=True, flatten=True)
	objs = cmds.ls(sl=True, objectsOnly=True)
	if objs and len(objs) == 1 and any('.' in item for item in selection):
		selected_components = selection

		bb = cmds.exactWorldBoundingBox() # if nothing is passed it will be based on selection
		pos = ((bb[0] + bb[3]) / 2, (bb[1] + bb[4]) / 2, (bb[2] + bb[5]) / 2)
		
		obj_shape = objs[0]
		objs = cmds.listRelatives(obj_shape, parent=True, fullPath=True)[0]
		cluster_node, cluster_transform = cmds.cluster(obj_shape)
		cluster_transform = cmds.rename(cluster_transform, objs.split('|')[-1] + '_cluster')
		cluster_node = get_cluster_node(cluster_transform)

		# Move cluster pivot to components center
		cluster_handle = cmds.listRelatives(cluster_transform, children=True, fullPath=True)[0]

		cmds.xform(cluster_transform, rotatePivot=pos)
		cmds.xform(cluster_transform, scalePivot=pos)
		cmds.setAttr(f"{cluster_handle}.origin", pos[0], pos[1], pos[2])

		cmds.select(selected_components)
		mel.eval("invertSelection")
		cmds.percent(cluster_node, v=0)
		mel.eval( f'artSetToolAndSelectAttr( "artAttrCtx", "cluster.{cluster_node}.weights" );' )
		mel.eval("ConvertSelectionToShell")
	else:
		# mel.eval('CreateCluster')
		if objs:
			cluster_node, cluster_transform = cmds.cluster()
			first_obj = objs[0]
			cluster_transform = cmds.rename(cluster_transform, first_obj.split('|')[-1] + '_cluster')
			obj_parent = cmds.listRelatives(first_obj, parent=True, fullPath=True)[0]
			cluster_transform = cmds.parent(cluster_transform, obj_parent)[0]

def get_input_geo_index(clusterFn, meshShape):
	for i, geo_mobj in enumerate(clusterFn.getInputGeometry()):
		geo_dag = om.MFnDagNode(geo_mobj)
		parent_obj = geo_dag.parent(0)
		parent_dag = om.MFnDagNode(parent_obj)
		transform_name = parent_dag.fullPathName()
		if transform_name == meshShape:
			return i, geo_dag.getPath()

def update_clusters_handle_position():
	selections = cmds.ls(sl=True)
	for selection in selections:
		if obj_is_type(selection, 'clusterHandle'):
			if cmds.nodeType(selection) == 'transform':
				clusterTransform = selection
				clusterHandle = cmds.listRelatives(selection, children=True, fullPath=True)[0]
			else:  # assume selected a `clusterHandle` node
				clusterTransform = cmds.listRelatives(selection, parent=True, fullPath=True)[0]
				clusterHandle = selection
			# rotatePivot = clusterTransform.scalePivot.get()
			rotatePivot = cmds.getAttr(f"{clusterTransform}.rotatePivot")[0]
			cmds.setAttr(f"{clusterHandle}.origin", rotatePivot[0], rotatePivot[1], rotatePivot[2])

def get_meshes_from_cluster_transform(clusterTransform):
	cluster_node = get_cluster_node(clusterTransform)
	if cluster_node:
		# meshes = cmds.deformer(cluster_node[0], query=True, geometry=True)
		meshes = cmds.listConnections(cluster_node+'.originalGeometry', type='mesh')  # could also use cmds.deformer?
		meshes = [cmds.ls(mesh, long=True)[0] for mesh in meshes]  # convert to long name
		return meshes

def get_mobject(node_name):
	sel = om.MSelectionList()
	sel.add(node_name)
	return sel.getDependNode(0)

def copy_cluster_weights(sourceCluster, sourceMesh, targetCluster, targetMesh):
	if not cmds.objExists(sourceMesh) or not cmds.objExists(targetMesh):
		cmds.warning(f"[copy_cluster_weights()] Object '{sourceMesh}' or '{targetMesh}' does not exist")
		return False
	
	sourceMesh = cmds.ls(sourceMesh, long=True)[0] # convert to full path name
	targetMesh = cmds.ls(targetMesh, long=True)[0]

	# Check if both meshes have the same point count
	source_num_verts = cmds.polyEvaluate(sourceMesh, vertex=True)
	target_num_verts = cmds.polyEvaluate(targetMesh, vertex=True)
	if source_num_verts != target_num_verts:
		cmds.warning(f"[copy_cluster_weights()] Meshes have different vertex counts. Source: {source_num_verts}, Target: {target_num_verts}")
		return False

	source_cluster = get_cluster_node(sourceCluster)
	target_cluster = get_cluster_node(targetCluster)

	# Create MFnWeightGeometryFilter for both clusters
	src_cluster_fn = oma.MFnWeightGeometryFilter(get_mobject(source_cluster))
	tgt_cluster_fn = oma.MFnWeightGeometryFilter(get_mobject(target_cluster))

	src_index, _ = get_input_geo_index(src_cluster_fn, sourceMesh)
	tgt_index, target_dagPath = get_input_geo_index(tgt_cluster_fn, targetMesh)

	# Get weights from source
	src_components = om.MFnSingleIndexedComponent()
	src_comp = src_components.create(om.MFn.kMeshVertComponent)

	# Now create the MFnMesh with the mesh shape node
	src_components.addElements(list(range(source_num_verts)))

	weights = src_cluster_fn.getWeights(src_index, src_comp)

	# Apply weights to target
	tgt_components = om.MFnSingleIndexedComponent()
	tgt_comp = tgt_components.create(om.MFn.kMeshVertComponent)
	tgt_components.addElements(list(range(target_num_verts)))

	tgt_cluster_fn.setWeight(target_dagPath, tgt_index, tgt_comp, weights)
	print(f"Weights copied from {source_cluster}'s {sourceMesh} to {target_cluster}'s {targetMesh}")

def mirror_cluster_weights(mesh, sourceCluster, targetCluster):
	mesh = cmds.ls(mesh, long=True)[0] # convert to full path name

	source_cluster = get_cluster_node(sourceCluster)
	target_cluster = get_cluster_node(targetCluster)

	num_verts = cmds.polyEvaluate(mesh, vertex=True)
	sym_table = get_symmetry_table(mesh)

	# Create MFnWeightGeometryFilter for both clusters
	src_cluster_fn = oma.MFnWeightGeometryFilter(get_mobject(source_cluster))
	tgt_cluster_fn = oma.MFnWeightGeometryFilter(get_mobject(target_cluster))

	src_index, _ = get_input_geo_index(src_cluster_fn, mesh)
	tgt_index, target_dagPath = get_input_geo_index(tgt_cluster_fn, mesh)

	# Get weights from source
	src_components = om.MFnSingleIndexedComponent()
	src_comp = src_components.create(om.MFn.kMeshVertComponent)
	src_components.addElements(list(range(num_verts)))
	weights = src_cluster_fn.getWeights(src_index, src_comp)
	mirrored_weights = om.MFloatArray([weights[sym_table[i]] for i in range(num_verts)])

	# Apply weights to target
	tgt_components = om.MFnSingleIndexedComponent()
	tgt_comp = tgt_components.create(om.MFn.kMeshVertComponent)
	tgt_components.addElements(list(range(num_verts)))

	tgt_cluster_fn.setWeight(target_dagPath, tgt_index, tgt_comp, mirrored_weights)
	# cmds.setAttr(f"{source_cluster}.envelope", saved_envelope_val)  # restore envelope value
	print(f"Cluster mirrored: {target_cluster}")

def mirror_name_for_cluster(clusterTransform):
	obj_basename = clusterTransform.split('|')[-1]
	new_name = mirror_name(obj_basename)
	if new_name != obj_basename:
		return obj_basename, new_name
	
	side = get_object_side(clusterTransform)
	
	if side == 'left':
		return f"{obj_basename}_left", f"{obj_basename}_right"
	
	if side == 'right':
		return f"{obj_basename}_right", f"{obj_basename}_left"
	
	return obj_basename, new_name

def mirror_cluster():
	selections = cmds.ls(sl=True)
	
	if not selections:
		return
	
	source_cluster_transform = selections[0]
	meshes = get_meshes_from_cluster_transform(source_cluster_transform)
	
	if not meshes:
		return
	
	source_cluster = get_cluster_node(source_cluster_transform)
	saved_envelope_val = cmds.getAttr(f"{source_cluster}.envelope")
	cmds.setAttr(f"{source_cluster}.envelope", 0)
	rotate_pivot = cmds.xform(source_cluster_transform, q=True, rotatePivot=True)
	rotate_pivot[0] *= -1  # invert X-Axis

	new_cluster, new_cluster_transform = cmds.cluster(meshes)
	
	# Mirror name for both clusters
	old_cluster_transform_name, new_cluster_transform_name = mirror_name_for_cluster(source_cluster_transform)
	source_cluster_transform = cmds.rename(source_cluster_transform, old_cluster_transform_name)
	source_cluster = get_cluster_node(source_cluster_transform)
	new_cluster_transform = cmds.rename(new_cluster_transform, new_cluster_transform_name)
	new_cluster = get_cluster_node(new_cluster_transform)  # cluster name is updated when the handle is renamed

	cmds.xform(new_cluster_transform, rotatePivot=rotate_pivot)
	cmds.xform(new_cluster_transform, scalePivot=rotate_pivot)
	cmds.setAttr(f"{new_cluster_transform}.origin", rotate_pivot[0], rotate_pivot[1], rotate_pivot[2])

	# Copy animation keys from source cluster to new cluster
	if cmds.keyframe(source_cluster, query=True, keyframeCount=True) > 0:
		# Get all animated attributes on the source cluster
		animated_attrs = cmds.listAnimatable(source_cluster)
		
		for attr in animated_attrs:
			# Get the attribute name without the node name
			attr_name = attr.split('.')[-1]
			target_attr = f"{new_cluster}.{attr_name}"
			
			# Check if this attribute exists on the target
			if cmds.objExists(target_attr):
				# Get all keyframes for this attribute
				keyframes = cmds.keyframe(attr, query=True, timeChange=True)
				
				if keyframes:
					# For each keyframe, copy the value and tangent information
					for frame in keyframes:
						# Get value at this frame
						value = cmds.getAttr(attr, time=frame)
						
						# Get in and out tangent types
						in_tangent = cmds.keyTangent(attr, query=True, time=(frame,frame), inTangentType=True)[0]
						out_tangent = cmds.keyTangent(attr, query=True, time=(frame,frame), outTangentType=True)[0]
						
						# Set keyframe on target with same value
						cmds.setKeyframe(target_attr, time=frame, value=value)
						
						# Set the same tangent types
						cmds.keyTangent(target_attr, time=(frame,frame), inTangentType=in_tangent, outTangentType=out_tangent)
	
	print(f"Animation keys copied from {source_cluster} to {new_cluster}")

	for mesh in meshes:
		num_verts = cmds.polyEvaluate(mesh, vertex=True)
		sym_table = get_symmetry_table(mesh)

		# Create MFnWeightGeometryFilter for both clusters
		src_cluster_fn = oma.MFnWeightGeometryFilter(get_mobject(source_cluster))
		tgt_cluster_fn = oma.MFnWeightGeometryFilter(get_mobject(new_cluster))

		src_index, _ = get_input_geo_index(src_cluster_fn, mesh)
		tgt_index, target_dagPath = get_input_geo_index(tgt_cluster_fn, mesh)

		# Get weights from source
		src_components = om.MFnSingleIndexedComponent()
		src_comp = src_components.create(om.MFn.kMeshVertComponent)
		src_components.addElements(list(range(num_verts)))
		weights = src_cluster_fn.getWeights(src_index, src_comp)
		mirrored_weights = om.MFloatArray([weights[sym_table[i]] for i in range(num_verts)])

		# Apply weights to target
		tgt_components = om.MFnSingleIndexedComponent()
		tgt_comp = tgt_components.create(om.MFn.kMeshVertComponent)
		tgt_components.addElements(list(range(num_verts)))

		tgt_cluster_fn.setWeight(target_dagPath, tgt_index, tgt_comp, mirrored_weights)
		# cmds.setAttr(f"{source_cluster}.envelope", saved_envelope_val)  # restore envelope value
		print(f"Cluster mirrored: {new_cluster_transform}")
	
	cmds.setAttr(f"{source_cluster}.envelope", saved_envelope_val)


def get_nth_shape_type(selections, n, shape_type):
	"""
	Returns the Nth transform node (1-based index) whose shape matches the given type.

	Args:
    n (int): 1-based index (e.g., 1 = first match).
    shape_type (str): Shape type to look for (e.g., 'mesh').
    selection (list): Optional list of transform nodes (defaults to cmds.ls(sl=True)).

	Returns:
	    str or None: The name of the transform node, or None if not found.
	"""
	matched = []

	for obj in selections:
		shapes = cmds.listRelatives(obj, shapes=True, fullPath=True) or []
		for shape in shapes:
			if cmds.nodeType(shape) == shape_type:
				matched.append(obj)
				break  # only add once even if multiple shapes match

	if 0 < n <= len(matched):
		return matched[n - 1]
	return None

def copy_cluster_weights_selected():
	selections = cmds.ls(sl=True)
	if len(selections) != 4:
		return
	source_mesh = get_nth_shape_type(selections, 1, 'mesh')
	target_mesh = get_nth_shape_type(selections, 2, 'mesh')
	source_clusterHandle = get_nth_shape_type(selections, 1, 'clusterHandle')
	target_clusterHandle = get_nth_shape_type(selections, 2, 'clusterHandle')
	copy_cluster_weights(source_clusterHandle, source_mesh, target_clusterHandle, target_mesh)

def mirror_cluster_weights_selected():
	selections = cmds.ls(sl=True)
	if len(selections) != 3:
		return
	source_mesh = get_nth_shape_type(selections, 1, 'mesh')
	source_clusterHandle = get_nth_shape_type(selections, 1, 'clusterHandle')
	target_clusterHandle = get_nth_shape_type(selections, 2, 'clusterHandle')
	mirror_cluster_weights(source_mesh, source_clusterHandle, target_clusterHandle)

def duplicate_cluster_setup():
	selections = cmds.ls(sl=True)
	
	if not selections:
		return
	
	meshes = get_meshes_from_cluster_transform(selections[0])
	if meshes:
		new_group = cmds.group(empty=True, name="new_cluster_setup")
		dups = []
		mesh_to_dup = {}
		for mesh in meshes:
			cmds.select(clear=True)
			dup = duplicate_base(mesh)
			dup = cmds.parent(dup, new_group)[0]
			dup = cmds.rename(dup, mesh.split("|")[-1])
			dups.append(dup)
			mesh_to_dup[mesh] = dup
		
		new_cluster, new_cluster_handle = cmds.cluster(dups, name=selections[0].split("|")[-1])
		source_cluster = get_cluster_node(selections[0])
		
		for mesh, dup in mesh_to_dup.items():
			copy_cluster_weights(source_cluster, mesh, new_cluster, dup)
		
		cmds.parent(new_cluster_handle, new_group)

def duplicate_cluster_setup2():
	selections = cmds.ls(sl=True)
	
	if not selections or not obj_is_type(selections[0], 'clusterHandle'):
		cmds.warning('[duplicate_cluster_setup2] Please select a cluster transform')
		return
	
	cluster_transform = selections[0]
	
	meshes = get_meshes_from_cluster_transform(cluster_transform)

	if not meshes:
		return
	
	# Prompt user for group name
	new_group_name = 'new_cluster_setup'
	result = cmds.promptDialog(
			title='Group Name',
			message='Enter name for the cluster setup group:',
			button=['OK', 'Cancel'],
			defaultButton='OK',
			cancelButton='Cancel',
			dismissString='Cancel'
	)
	
	# Check if user clicked OK
	if result != 'Cancel':
		new_group_name = cmds.promptDialog(query=True, text=True)

	new_group = cmds.group(empty=True, name=new_group_name)
	dups = []
	mesh_to_dup = {}
	for mesh in meshes:
		cmds.select(clear=True)
		dup = duplicate_base(mesh)
		dup = cmds.parent(dup, new_group)[0]
		dup = cmds.rename(dup, mesh.split("|")[-1])
		dups.append(dup)
		mesh_to_dup[mesh] = dup
	
	new_cluster, new_cluster_handle = cmds.cluster(dups, name=selections[0].split("|")[-1])
	source_cluster = get_cluster_node(selections[0])
	
	for mesh, dup in mesh_to_dup.items():
		copy_cluster_weights(source_cluster, mesh, new_cluster, dup)
	
	new_cluster_handle = cmds.parent(new_cluster_handle, new_group)[0]
	new_cluster_handle = cmds.rename(new_cluster_handle, f"{new_group_name.lower()}_cluster")
	rotatePivot = cmds.getAttr(f"{cluster_transform}.rotatePivot")[0]
	cmds.setAttr(f"{new_cluster_handle}.rotatePivot", rotatePivot[0], rotatePivot[1], rotatePivot[2])
	cmds.setAttr(f"{new_cluster_handle}.scalePivot", rotatePivot[0], rotatePivot[1], rotatePivot[2])
	cmds.setAttr(f"{new_cluster_handle}.origin", rotatePivot[0], rotatePivot[1], rotatePivot[2])

	new_cluster = get_cluster_node(new_cluster_handle)
	cmds.setKeyframe(f'{new_cluster}.envelope', time=1, value=0.0)
	cmds.setKeyframe(f'{new_cluster}.envelope', time=10, value=1.0)

def setup_cluster_symmetry():
	selection = cmds.ls(sl=True)
	if len(selection) == 2 and get_cluster_node(selection[0]) and get_cluster_node(selection[1]):
		left_cluster_transform = selection[0]
		right_cluster_transform = selection[1]
		expression_name = f"sym_cluster_{left_cluster_transform.split('|')[-1]}"
		expression_string = f"""
			{right_cluster_transform}.translateX = -{left_cluster_transform}.translateX;
			{right_cluster_transform}.translateY = {left_cluster_transform}.translateY;
			{right_cluster_transform}.translateZ = {left_cluster_transform}.translateZ;
			{right_cluster_transform}.rotateX = {left_cluster_transform}.rotateX;
			{right_cluster_transform}.rotateY = -{left_cluster_transform}.rotateY;
			{right_cluster_transform}.rotateZ = -{left_cluster_transform}.rotateZ;
			{right_cluster_transform}.scaleX = {left_cluster_transform}.scaleX;
			{right_cluster_transform}.scaleY = {left_cluster_transform}.scaleY;
			{right_cluster_transform}.scaleZ = {left_cluster_transform}.scaleZ;
		"""
		cmds.expression(s=expression_string, name=expression_name)


# _____________________________________ AUTO SELECT PAINT TOOL  _____________________________________

def activate_paint_tool(deformer, button=None):
	if cmds.nodeType(deformer) == 'cluster':
		mel.eval( f'artSetToolAndSelectAttr( "artAttrCtx", "cluster.{deformer}.weights" );' )
		
	elif cmds.nodeType(deformer) == 'blendShape':
		# mel.eval( f'artSetToolAndSelectAttr( "artAttrCtx", "blendShape.{deformer}.paintTargetWeights" );' )
		mel.eval( f'artSetToolAndSelectAttr( "artAttrCtx", "blendShape.{deformer}.weights" );' )  # paint on base weights instead of target weights
	
	return True

class SelectPaintToolMenu(QuickMenu):
	def __init__(self, deformer_list):
		super().__init__('Select Paint Tool')
		
		for deformer in deformer_list:
			hotkey_prefix = ''

			if cmds.nodeType(deformer) == 'cluster':
				hotkey_prefix = '( C ) '
			elif cmds.nodeType(deformer) == 'blendShape':
				hotkey_prefix = '( B ) '

			self.create_button(f"{hotkey_prefix}{deformer}", partial(activate_paint_tool, deformer))

		self.focus_first_button()
		self.show()

def auto_select_paint_tool():
	"""
	Active paint tool, 
	 * currently supports only Paint Skin, Blendshape and Cluster
	"""
	selections = cmds.ls(sl=True)

	if not selections:
		cmds.warning("Please select a geometry")
		return None

	first_selection = selections[0]  # only works on the first selection
	
	if not is_selection_geometry_node(first_selection):
		cmds.warning("Selection is not a geometry")
		return

	deformers = [d for d in get_deformers_from_object(first_selection) if cmds.nodeType(d) not in ('tweak',)]

	if not deformers:
		cmds.warning("No deformers found in selected geometry")
		return None

	if len(deformers) == 1:
		activate_paint_tool(deformers[0])
	else:
		open_quick_menu(SelectPaintToolMenu, deformers)

	# if len(selections) == 1 \
	# 	and type(selections[0]) == pm.nodetypes.Transform \
	# 	and type(selections[0].getShape()) == pm.nodetypes.ClusterHandle:
	# 		cluster_handle = selections[0].getShape()
	# 		cluster_node = cluster_handle.listConnections(type='cluster')
	# 		if cluster_node:
	# 			cluster_node = cluster_node[0]
	# 			geos = cluster_node.getOutputGeometry()
	# 			pm.select(geos[0])
	# 			mel.eval('PaintClusterWeightsTool')
	# 			mel.eval('artSetToolAndSelectAttr( "artAttrCtx", "cluster.{}.weights" );'.format(cluster_node.name()))
	# elif len(selections) == 2 \
	# 		and isinstance(selections[0], pm.nodetypes.Transform) \
	# 		and isinstance(selections[0].getShape(), pm.nodetypes.ClusterHandle) \
	# 		and isinstance(selections[1], pm.nodetypes.Transform) \
	# 		and isinstance(selections[1].getShape(), pm.nodetypes.Mesh):
	# 	mel.eval('workspaceControl -e -restore ToolSettings;')
	# 	mel.eval('PaintClusterWeightsTool')
	# 	the_cluster = selections[0].getShape().outputs(type='cluster')[0]
	# 	mel.eval('artSetToolAndSelectAttr( "artAttrCtx", "cluster.{}.weights" );'.format(the_cluster.name()))
	# 	return True
	# else:
	# 	for i in selections:
	# 		history = pm.listHistory(i)
	# 		deform_history = pm.ls(history, type='geometryFilter')
	# 		if deform_history and len(deform_history) >= 1:
	# 			mel.eval('workspaceControl -e -restore ToolSettings;')
	# 			for each_deformer in deform_history:
	# 				if isinstance(each_deformer, pm.nodetypes.SkinCluster):
	# 					hist = cmds.listHistory(each_deformer.name(), future=True, levels=1)
	# 					customNodeTypes = ['ngst2MeshDisplay', 'ngst2SkinLayerData']
	# 					for i in hist:
	# 						# if ngskin custom node found
	# 						if cmds.nodeType(i) in customNodeTypes:
	# 							import ngSkinTools2.ui.hotkeys
	# 							ngSkinTools2.ui.hotkeys.paint_tool_start()
	# 							return True
	# 					mel.eval('ArtPaintSkinWeightsToolOptions')
	# 					return True
	# 				elif isinstance(each_deformer, pm.nodetypes.BlendShape):
	# 					mel.eval('ArtPaintBlendShapeWeightsTool')
	# 					return True
	# 				elif isinstance(each_deformer, pm.nodetypes.Cluster):
	# 					mel.eval('PaintClusterWeightsTool')
	# 					return True


# _____________________________________ KEY ENVELOPE  _____________________________________

def key_envelope():
	selections = cmds.ls(sl=True, objectsOnly=True)
	
	all_are_transforms = True
	for obj in selections:
		if cmds.nodeType(obj) != 'transform':
			all_are_transforms = False
			break
	
	if all_are_transforms:
		all_deformers = set()
		for i in cmds.ls(sl=True, objectsOnly=True):
			deformers = get_deformers_from_object(i)
			all_deformers.update(deformers)
	
		for d in all_deformers:
			if(cmds.attributeQuery('envelope', node=d, exists=True)):
				cmds.setKeyframe(f'{d}.envelope', time=1, value=0.0)
				cmds.setKeyframe(f'{d}.envelope', time=10, value=1.0)
	
	else:
		for obj in selections:
			if(cmds.attributeQuery('envelope', node=obj, exists=True)):
				cmds.setKeyframe(f'{obj}.envelope', time=1, value=0.0)
				cmds.setKeyframe(f'{obj}.envelope', time=10, value=1.0)

	# Refresh timeline
	current_time = cmds.currentTime(query=True)	
	cmds.currentTime(int(current_time)+1, edit=True)
	cmds.currentTime(int(current_time), edit=True)

# _____________________________________ WRAP  _____________________________________

def wrap_selected():
	selection = cmds.ls(sl=True, objectsOnly=True)
	if len(selection) < 2:
		return
	
	objs = selection[:-1]
	master = selection[-1]

	# Create the group if it doesn't exist
	wrap_base_group = '__wrap_base'
	if not cmds.objExists(wrap_base_group):
		wrap_base_group = cmds.group(empty=True, name=wrap_base_group)

	for obj in objs:
		cmds.select([obj, master], replace=True)
		# C:\Program Files\Autodesk\Maya2025\scripts\others\doWrapArgList.mel
		# createWrap($threshold, $maxDist, $inflType, $exclusiveBind, $autoWeightThreshold, $renderInfl, $falloffMode)
		threshold = 0
		maxDist = 20
		inflType = 2
		exclusiveBind = 0
		autoWeightThreshold = 0
		renderInfl = 0
		falloffMode = 0
		wrap_node = mel.eval(f'doWrapArgList "7" {{ "1","{threshold}","{maxDist}", "{inflType}", "{exclusiveBind}", "{autoWeightThreshold}", "{renderInfl}", "{falloffMode}" }}')[0]
		wrap_base = cmds.listConnections(f"{wrap_node}.basePoints")[0]

		cmds.parent(wrap_base, wrap_base_group)
