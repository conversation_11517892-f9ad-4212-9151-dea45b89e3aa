
class StringArray():
	"""Convenient class for using optionVar's stringArray"""
	def __init__(self, name, num_of_max_items=0):
		self.name = name
		if not cmds.optionVar(exists=name):
			cmds.optionVar(stringArray=name)
	
	def append(self, str_value: str):
		if cmds.optionVar(exists=self.name):
			for each_str in cmds.optionVar(q=self.name):
				# avoid duplicates
				if str_value == each_str:
					return None
			cmds.optionVar(sva=(self.name, str_value))
	
	def __len__(self):
		return len(self.items())

	def __getitem__(self, i):
		string_array = self.items()
		if string_array and i < len(string_array):
			return string_array[i]
	
	def pop(self, id):  # id is zero-based
		item = self[id]
		cmds.optionVar(removeFromArray=(self.name, id))
		return item

	def items(self):
		if cmds.optionVar(exists=self.name):
			return cmds.optionVar(q=self.name)
		else:
			self.clear()
			return []

	def clear(self):
		cmds.optionVar(stringArray=self.name)

	def __repr__(self):
		"""Shows better info than: <__main__.StringArray object at 0x000001AC2C493CD0>"""
		return f"StringArray(items={self.items()})"
	
	def __str__(self):
		"""similar to str(list)"""
		return str(self.items())

	# def __delitem__(self, i): del self.list[i]

	# def __setitem__(self, i, v):
	# 		self.check(v)
	# 		self.list[i] = v

	# def insert(self, i, v):
	# 		self.check(v)
	# 		self.list.insert(i, v)
