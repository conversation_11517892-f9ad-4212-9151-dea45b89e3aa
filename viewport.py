import maya.mel as mel
import maya.cmds as cmds


def get_cam_from_view():
	p = cmds.getPanel(withFocus=True)
	if cmds.getPanel(typeOf=p) == "modelPanel":
		cam = cmds.modelEditor(p, q=True, activeView=True, cam=True)
		return cam
	return None


def toggle_front_persp():
	view = get_cam_from_view()
	if view == "persp":
		mel.eval('dR_DoCmd("viewFront")')
	else:
		mel.eval('dR_DoCmd("viewPersp")')


def toggle_left_persp():
	view = get_cam_from_view()
	if view == "persp":
		mel.eval('dR_DoCmd("viewLeft")')
	else:
		mel.eval('dR_DoCmd("viewPersp")')


def toggle_model_panel_option(option, allPanels=False):
	"""
	Options: joints, polymeshes, nurbsCurves, selectionHiliteDisplay, locators, useDefaultMaterial, xray
	"""
	panels = []
	if allPanels:
		panels = cmds.getPanel(visiblePanels=True)
	else:
		panels.append(cmds.getPanel(withFocus=True))
	for p in panels:
		if cmds.getPanel(typeOf=p) == "modelPanel":
			status = mel.eval(f"modelEditor -q -{option} {p}")
			mel.eval(f"modelEditor -e -{option} {int(not status)} {p}")


def flat_lighting():
	visible_panels = cmds.getPanel(visiblePanels=True)
	for panel in visible_panels:
		if cmds.getPanel(typeOf=panel) == "modelPanel":
			cmds.modelEditor(panel, displayAppearance="smoothShaded", e=True)
			cmds.modelEditor(panel, displayLights="flat", e=True)
			cmds.modelEditor(panel, displayTextures=True, e=True)


def create_custom_camera_window(
	window_name="customCamWindow",
	panel_name="customCamPanel",
	camera="camera1",
	width=800,
	height=450,
):
	if not cmds.window(window_name, exists=True):
		# Check if camera exists, use it if it does, otherwise create a new one
		if cmds.objExists(camera):
			cam_transform = camera
			# Get the shape node if we have the transform
			shapes = cmds.listRelatives(camera, shapes=True, type="camera")
			cam_shape = shapes[0] if shapes else None
		else:
			# Create a new camera with the provided name
			cam_transform, cam_shape = cmds.camera(name=camera)

		# Create window and layout
		cmds.window(window_name, width=width, height=height, title=f"{camera} Window")
		pane_layout = cmds.paneLayout()

		# Check if model panel exists
		if not cmds.modelPanel(panel_name, exists=True):
			cmds.modelPanel(panel_name, camera=cam_transform)
		else:
			cmds.modelPanel(panel_name, edit=True, parent=pane_layout)
			cmds.modelEditor(
				cmds.modelPanel(panel_name, query=True, modelEditor=True),
				edit=True,
				camera=cam_transform,
			)

		# Show window
		cmds.showWindow(window_name)

		# Customize model editor
		cmds.modelEditor(
			cmds.modelPanel(panel_name, query=True, modelEditor=True),
			edit=True,
			nurbsCurves=False,
			grid=False,
			displayAppearance="smoothShaded",
			displayTextures=True,
			headsUpDisplay=False,
			deformers=False,
			joints=False,
		)

		return cam_transform  # Return the camera transform node if needed
