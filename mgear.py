from pathlib import Path

import maya.cmds as cmds
import pymel.core as pm

def set_mgear_shifter_custom_path():
	# Check if guide node exists
	if not cmds.objExists("guide"):
		cmds.warning("Guide node does not exist in the scene")
		return

	# Create the attribute if it doesn't exist
	if not cmds.objExists("guide.mGearShifterCustomStepPath"):
		cmds.addAttr("guide", longName="mGearShifterCustomStepPath", dataType="string")

	# Get current scene path using pathlib
	current_scene = cmds.file(query=True, sceneName=True)

	if not current_scene:
		cmds.warning("No scene is currently open or saved")
		return

	scene_path = Path(current_scene).absolute()

	# Initialize default path as the scene's parent folder
	default_path = scene_path.parent

	# Try to find "Maya/rig-build" in the path
	rig_build_path = None
	for parent in scene_path.parents:
		if parent.name.lower() == "maya":
			potential_rig_build = parent / "rig-build"
			if potential_rig_build.exists():
				rig_build_path = potential_rig_build
				break

	# Set the attribute value
	target_path = rig_build_path if rig_build_path else default_path
	cmds.setAttr(
		"guide.mGearShifterCustomStepPath",
		str(target_path).replace("\\", "/"),
		type="string",
	)

	# Print result
	result_type = "rig-build folder" if rig_build_path else "scene folder"
	cmds.warning(f"Set mGearShifterCustomStepPath to {result_type}: {target_path}")

def select_all_mgear_ctls():
	"""
	Select All mGear Ctls
	"""
	rig_controllers_grp = pm.ls("::rig_controllers_grp")
	if rig_controllers_grp:
		pm.select(cl=True)
		# Note: there could be multiple mgear rigs in the scene, and this will select all of them
		for ctls_grp in rig_controllers_grp:
			if isinstance(ctls_grp, pm.nodetypes.ObjectSet):
				pm.select(ctls_grp.members(), add=True)

def toggle_all_mgear_ctls_visibility():
	"""
	"""
	rigs = pm.ls("::rig")
	for i in rigs:
		if i.hasAttr("is_rig"):
			vis_status = pm.getAttr(i.name() + '.ctl_vis')
			pm.setAttr(i.name() + '.ctl_vis', not vis_status)
			pm.setAttr(i.name() + '.jnt_vis', not vis_status)

def get_namespace_from_selection():
	# Get selected object's namespace
	selections = pm.selected()
	if len(selections) == 1:
		objname = selections[0].nodeName()
		if ':' in objname:
			return objname.split(':')[0]
	return None

def get_ctl_with_namespace(ctl_name):
	# Returns ctl_name or namespace:ctl_name
	if cmds.objExists(ctl_name):
		return ctl_name
	else:
		the_namespace = get_namespace_from_selection()
		if the_namespace:
			namespace_ctl_name = the_namespace + ':' + ctl_name
			if cmds.objExists(namespace_ctl_name):
				return namespace_ctl_name
		return None

def toggle_arms_ikfk():
	"""
	Toggle Arms IKFK
	"""
	for side in ['L0', 'R0', 'l', 'r']:
		for ctl in ['ctl', 'control']:
			ctl = get_ctl_with_namespace("armUI_{}_{}".format(side, ctl))
			if ctl and cmds.objExists(ctl):
				state = cmds.getAttr(ctl + ".arm_blend")
				cmds.setAttr(ctl + ".arm_blend", not state)

def toggle_legs_ikfk():
	"""
	Toggle Legs IKFK
	"""
	for side in ["L0", "R0", "l", "r"]:
		for ctl in ["ctl", "control"]:
			ctl = get_ctl_with_namespace("legUI_{}_{}".format(side, ctl))
			if ctl and cmds.objExists(ctl):
				state = cmds.getAttr(ctl + ".leg_blend")
				cmds.setAttr(ctl + ".leg_blend", not state)


def toogle_guide():
	if pm.objExists("guide"):
		state = pm.PyNode("guide").visibility.get()
		pm.PyNode("guide").visibility.set(not state)


def get_namespace(object_name):
	if len(object_name.split(":")) >= 2:
		return ":".join(object_name.split(":")[:-1])
	else:
		return ""


def reset_bind_pose():
	all_poses = pm.ls(type="dagPose") or []
	# bind_poses = [k for k in all_poses if k.bindPose.get()]

	for bp in all_poses:
		# if 'dagPose' in bp.name():
		try:
			pm.dagPose(bp, restore=True)
		except:
			pass