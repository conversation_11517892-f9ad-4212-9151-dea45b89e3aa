import re
import os
import importlib

# import maya.mel as mel
import maya.cmds as cmds
import maya.OpenMayaUI as omui
from maya.app.general.mayaMixin import MayaQWidgetDockableMixin

from PySide6 import QtWidgets

# Import additional modules for layer view
from shiboken6 import wrapInstance
from PySide6.QtCore import Qt, QObject, QEvent, QModelIndex, QSize
from PySide6.QtWidgets import QWidget, QMainWindow, QHeaderView, QStyledItemDelegate, QApplication, QTableWidgetItem
from PySide6.QtGui import QAction, QCursor, QColor, QBrush

# Import our custom classes from separate files
from . import mytablewidget
from . import layer_visibility_delegate
from . import blendshapesEditor_ui
from .. import blendshapes
from ..ui import pyflow_widgets
from ..utils import obj_is_type, UndoStack

# Import mgear callback manager
from ..mgear_callbackManager import CallbackManager

importlib.reload(mytablewidget)
importlib.reload(layer_visibility_delegate)
importlib.reload(blendshapesEditor_ui)
importlib.reload(blendshapes)

from .layer_visibility_delegate import LayerVisibilityDelegate
from ..blendshapes import select_blendshape_mesh, get_blendshape_nodes

THIS_FILE_DIR = os.path.split(os.path.realpath(__file__))[0]

EXTRACT_TABLE_ROW_HEIGHT = 14

def error(msg):
	cmds.confirmDialog(
			title='Error',
			message=msg,
			button=['OK'],
			defaultButton='OK'
	)
	cmds.error(msg)

def maya_main_window():
	return wrapInstance(int(omui.MQtUtil.mainWindow()), QWidget)

class BlendshapesEditor_Window(MayaQWidgetDockableMixin, QMainWindow):
	def __init__(self, parent=maya_main_window()):
		super(BlendshapesEditor_Window,self).__init__(parent)

		self.setObjectName("BlendshapesEditorWindow")
		
		# SetupUi() makes widget available to self
		#----------------------------------------------------------------------
		self.ui = blendshapesEditor_ui.Ui_MainWindow()
		self.ui.setupUi(self)
		
		# Create visibility delegate
		self.visibility_delegate = LayerVisibilityDelegate(self.ui.targets_table)
		
		# Fixed square size for UI elements
		self._fixed_square = 22
		
		# Setup callback manager for Maya selection changes and new scene
		self.callback_manager = CallbackManager()
		# self.callback_manager.debug = True  # Enable debug mode to see callback info
		self.callback_manager.selectionChangedCB("blendshapesEditor", self.refresh_blendshape_list)
		self.callback_manager.newSceneCB("blendshapesEditorNewScene", self.on_new_scene)
		
		# Populate blendshape nodes list
		self.populate_viewer_list()
		
		# Connect signals
		self.ui.blendshape_nodes_filter_le.textChanged.connect(self.filter_blendshape_nodes)
		self.ui.blendshape_nodes_list.itemSelectionChanged.connect(self.on_blendshape_selected)
		self.ui.targets_filter_le.textChanged.connect(self.filter_targets)
		self.ui.extract_btn.clicked.connect(self.extract_blendshapes)
		
		# Setup targets table
		self.setup_targets_table()
		
		# Setup context menu for targets table
		self.ui.targets_table.setContextMenuPolicy(Qt.CustomContextMenu)
		self.ui.targets_table.customContextMenuRequested.connect(self.show_targets_table_context_menu)
		
		# Install event filter for middle-click on blendshape nodes list
		self.ui.blendshape_nodes_list.viewport().installEventFilter(self)
		
		# Connect tab widget signal for tab changes
		self.ui.tabs.currentChanged.connect(self.on_tab_changed)

		# Connect extract combobox signal
		self.ui.extract_cb.currentIndexChanged.connect(self.populate_extract_table)

		# Collapse the splitter
		self.ui.splitter.setSizes([0, 1])  # This collapses the first widget and gives all space to the second

		# Setup the extract tab
		self.setup_extract_tab()

		self.on_new_scene()
		self.refresh_blendshape_list()
		
		self.show(dockable=True)

		# Dock to the same space as Channel Box Control
		workspace_control_name = self.objectName() + "WorkspaceControl"
		cmds.workspaceControl(workspace_control_name, e=True, tabToControl=["ChannelBoxLayerEditor", -1])

	# _____________________________________ TARGETS TABLE  _____________________________________

	def setup_targets_table(self):
		"""Setup the targets table columns and properties"""
		self.ui.targets_table.setColumnCount(3)
		self.ui.targets_table.setHorizontalHeaderLabels(["", "Target", "Weight"])
		
		# Configure header and set column sizes
		header = self.ui.targets_table.horizontalHeader()
		header.setSectionResizeMode(0, QHeaderView.Fixed)
		header.setSectionResizeMode(1, QHeaderView.Stretch)
		header.setSectionResizeMode(2, QHeaderView.Stretch)
		header.setMinimumSectionSize(10)  # Allow smaller section sizes
		header.resizeSection(0, 24)  # Set visibility column width

		# Set selection behavior
		self.ui.targets_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
		
		# Set the visibility delegate for the first column
		self.ui.targets_table.setItemDelegateForColumn(0, self.visibility_delegate)
		
		# Enable editing for the table, but only for specific cells
		self.ui.targets_table.setEditTriggers(QtWidgets.QAbstractItemView.DoubleClicked | 
											 QtWidgets.QAbstractItemView.EditKeyPressed)
		
		# Connect to itemChanged signal to handle target renaming
		self.ui.targets_table.itemChanged.connect(self.on_target_renamed)
		
		# Scale down row height
		self.ui.targets_table.verticalHeader().setMaximumSectionSize(18)  # Adjust this value as needed
		
		# Connect key press event for row reordering
		self.ui.targets_table.keyPressEvent = self.table_key_press_event
		
		# Add flag to control multi-slider updates
		self.doUpdateHighlightedSliders = True

	def populate_targets_table(self, blendshape_node):
		"""Populate the targets table with blendshape targets"""
		# Temporarily disconnect the itemChanged signal to prevent triggering on_target_renamed
		self.ui.targets_table.itemChanged.disconnect(self.on_target_renamed)
		
		self.ui.targets_table.clearContents()
		self.ui.targets_table.setRowCount(0)
		
		# Get all targets for the selected blendshape node
		targets = cmds.listAttr(blendshape_node + '.w', multi=True)
		if not targets:
			# Reconnect the signal before returning
			self.ui.targets_table.itemChanged.connect(self.on_target_renamed)
			return
		
		# Filter targets if there's a filter text
		filter_text = self.ui.targets_filter_le.text().lower()
		if filter_text:
			targets = [t for t in targets if filter_text in t.lower()]
		
		# Set row count
		self.ui.targets_table.setRowCount(len(targets))
		
		# Fill table with target data
		for i, target in enumerate(targets):
			# Set row height
			self.ui.targets_table.setRowHeight(i, self._fixed_square)
			
			# Get current weight value
			try:
				weight = cmds.getAttr(f"{blendshape_node}.{target}")
			except:
				weight = 0.0
			
			# Column 0: Visibility checkbox (handled by delegate)
			visibility_item = QtWidgets.QTableWidgetItem()
			# Set visibility based on weight value
			is_visible = weight > 0.0
			visibility_item.setData(Qt.DisplayRole, is_visible)
			# Make the visibility item non-editable
			visibility_item.setFlags(visibility_item.flags() & ~Qt.ItemIsEditable)
			self.ui.targets_table.setItem(i, 0, visibility_item)
			
			# Column 1: Target name
			name_item = QtWidgets.QTableWidgetItem(target)
			# Ensure the name item is editable
			name_item.setFlags(name_item.flags() | Qt.ItemIsEditable)
			self.ui.targets_table.setItem(i, 1, name_item)
			
			# Column 2: Weight slider
			# Create slider widget
			slider = self.create_weight_slider(blendshape_node, target, weight)
			self.ui.targets_table.setCellWidget(i, 2, slider)
		
		# Reconnect the signal at the end of the function
		self.ui.targets_table.itemChanged.connect(self.on_target_renamed)

	def create_weight_slider(self, blendshape_node, target_name, weight):
		"""Create a weight slider widget for the table cell"""
		# Create a signal for slider moved (can be None)
		sliderMoved = None
		
		# Create the slider
		slider = pyflow_widgets.pyf_Slider(
			self.ui.targets_table,
			sliderMoved,
			Type="float",
			defaultValue=weight,
			sliderRange=(0.0, 1.0),
		)
		
		# Set height constraints
		slider.setMaximumHeight(self._fixed_square)
		slider.setMinimumHeight(self._fixed_square)
		slider.sld.setMaximumHeight(self._fixed_square)
		slider.sld.setMinimumHeight(self._fixed_square)
		slider.input.setMaximumHeight(self._fixed_square)
		slider.input.setMinimumHeight(self._fixed_square)
		
		# Store the row index for later use
		row = -1
		for i in range(self.ui.targets_table.rowCount()):
			if self.ui.targets_table.cellWidget(i, 2) == slider:
				row = i
				break
		
		# Connect signals for updating the value
		def update_weight(value):
			try:
				cmds.setAttr(f"{blendshape_node}.{target_name}", value)
				
				# Find the row of this slider if not already known
				nonlocal row
				if row == -1:
					for i in range(self.ui.targets_table.rowCount()):
						if self.ui.targets_table.cellWidget(i, 2) == slider:
							row = i
							break
				
				# Update visibility state if value is 0.0 or greater than 0.0
				if row != -1:
					visibility_item = self.ui.targets_table.item(row, 0)
					if visibility_item:
						# Set visibility based on whether value is greater than 0
						is_visible = value > 0.0
						visibility_item.setData(Qt.DisplayRole, is_visible)
						
			except Exception as e:
				print(f"Error setting weight: {e}")
		
		def open_undo_chunk():
			cmds.undoInfo(openChunk=True)
			
		def close_undo_chunk():
			cmds.undoInfo(closeChunk=True)
		
		# Connect signals
		slider.valueChanged.connect(update_weight)
		slider.valueChanged.connect(self.updateHighlightedSliders)
		slider.sliderPressed.connect(open_undo_chunk)
		slider.sliderReleased.connect(close_undo_chunk)
		
		# Connect editing signals for the slider
		sld = slider.findChild(pyflow_widgets.slider)
		if sld:
			sld.editingStart.connect(open_undo_chunk)
			sld.editingFinished.connect(close_undo_chunk)
		
		return slider

	def updateHighlightedSliders(self, value):
		"""Update all selected sliders when one is moved"""
		if not self.doUpdateHighlightedSliders:
			return
			
		# Get all selected rows
		selected_rows = set()
		for index in self.ui.targets_table.selectedIndexes():
			selected_rows.add(index.row())
		
		# For combobox and checkbox, open undo chunk before the loop
		if isinstance(self.sender(), (QtWidgets.QComboBox, QtWidgets.QCheckBox)):
			cmds.undoInfo(openChunk=True)
		
		# Get the blendshape node
		blendshape_node = None
		selected_items = self.ui.blendshape_nodes_list.selectedItems()
		if selected_items:
			blendshape_node = selected_items[0].text()
		
		for row in selected_rows:
			slider = self.ui.targets_table.cellWidget(row, 2)
			sender_type = type(self.sender())
			
			# Skip if this is the sender or not the right type
			if slider == self.sender() or not isinstance(slider, pyflow_widgets.pyf_Slider):
				continue
				
			# Disconnect signal temporarily
			slider.valueChanged.disconnect(self.updateHighlightedSliders)
			
			# Update slider value
			slider.setValue(value)
			
			# Update Maya attribute directly
			target_name = self.ui.targets_table.item(row, 1).text()
			if blendshape_node and target_name:
				try:
					cmds.setAttr(f"{blendshape_node}.{target_name}", value)
				except Exception as e:
					print(f"Error setting weight: {e}")
			
			# Update visibility state if value is 0.0 or greater than 0.0
			visibility_item = self.ui.targets_table.item(row, 0)
			if visibility_item:
				# Set visibility based on whether value is greater than 0
				is_visible = value > 0.0
				visibility_item.setData(Qt.DisplayRole, is_visible)
			
			# Reconnect signal
			slider.valueChanged.connect(self.updateHighlightedSliders)
		
		# Close undo chunk if needed
		if isinstance(self.sender(), (QtWidgets.QComboBox, QtWidgets.QCheckBox)):
			cmds.undoInfo(closeChunk=True)

	def clear_targets_table(self):
		"""Clear the targets table"""
		self.ui.targets_table.clearContents()
		self.ui.targets_table.setRowCount(0)

	def on_target_renamed(self, item):
		"""Handle renaming of a target in the table"""
		# Only process changes to column 1 (Target name)
		if item.column() != 1:
			return
			
		# Get the blendshape node
		blendshape_node = None
		selected_items = self.ui.blendshape_nodes_list.selectedItems()
		if not selected_items:
			return
		blendshape_node = selected_items[0].text()
		
		# Get the row and new target name
		row = item.row()
		new_target_name = item.text()
		
		# Temporarily disconnect the itemChanged signal to prevent recursion
		self.ui.targets_table.itemChanged.disconnect(self.on_target_renamed)
		
		# Get all targets for the blendshape node
		all_targets = cmds.listAttr(f"{blendshape_node}.w", multi=True)
		if not all_targets or row >= len(all_targets):
			return
		
		# Get the old target name
		old_target_name = all_targets[row]
		
		# Call the utility function to rename the target
		success, message, current_weight = blendshapes.rename_blendshape_target(
			blendshape_node, old_target_name, new_target_name, row
		)
		
		if not success:
			# Revert to old name and show warning
			item.setText(old_target_name)
			cmds.warning(message)
			
		# Reconnect the itemChanged signal
		self.ui.targets_table.itemChanged.connect(self.on_target_renamed)

	def show_targets_table_context_menu(self, position):
		"""Show the context menu at the requested position"""
		# Create context menu
		context_menu = QtWidgets.QMenu(self)
		
		# Add action to print selected items
		print_action = QAction("Print Selected Items", self)
		print_action.triggered.connect(self.print_selected_items)
		context_menu.addAction(print_action)
		
		# Show the menu at the cursor position
		context_menu.exec_(QCursor.pos())

	# _____________________________________ FILTERS  _____________________________________

	def filter_targets(self):
		"""Filter targets based on the filter text"""
		selected_items = self.ui.blendshape_nodes_list.selectedItems()
		if selected_items:
			blendshape_node = selected_items[0].text()
			self.populate_targets_table(blendshape_node)

	def filter_blendshape_nodes(self, filter_text):
		"""Filter the blendshape nodes list based on the filter text"""
		self.ui.blendshape_nodes_list.clear()
		
		# Get all blendshape nodes
		blendshape_nodes = cmds.ls(type="blendShape")
		
		# Filter nodes based on the filter text
		filtered_nodes = [node for node in blendshape_nodes if filter_text.lower() in node.lower()]
		
		# Add filtered nodes to the list widget
		self.ui.blendshape_nodes_list.addItems(filtered_nodes)

	# _____________________________________ OTHERS  _____________________________________

	def on_tab_changed(self, index):
		"""Handle tab changes in the UI"""
		tab_name = self.ui.tabs.tabText(index)
		
		# Check if we're switching to the Extract tab
		if tab_name == "Extract":
			self.populate_extract_combo_box()
			self.populate_extract_table()  # Populate the tree with targets
		elif tab_name == "Node Viewer":
			self.populate_viewer_list()
		
		self.refresh_blendshape_list()

	def populate_viewer_list(self):
		"""Get all blendshape nodes in the scene and populate the list widget"""
		self.ui.blendshape_nodes_list.clear()
		
		# Get all blendshape nodes in the scene
		blendshape_nodes = cmds.ls(type="blendShape")
		
		# Add them to the list widget
		self.ui.blendshape_nodes_list.addItems(blendshape_nodes)

	def on_blendshape_selected(self):
		"""Handle selection change in the blendshape nodes list"""
		selected_items = self.ui.blendshape_nodes_list.selectedItems()
		if selected_items:
			blendshape_node = selected_items[0].text()
			self.populate_targets_table(blendshape_node)
		else:
			self.clear_targets_table()
	
	def print_selected_items(self):
		"""Print the names of selected items in the targets table"""
		selected_rows = set()
		for index in self.ui.targets_table.selectedIndexes():
			selected_rows.add(index.row())
		
		# Get the blendshape node
		blendshape_node = None
		selected_items = self.ui.blendshape_nodes_list.selectedItems()
		if selected_items:
			blendshape_node = selected_items[0].text()
		
		# Print selected items
		print("\n--- Selected Blendshape Targets ---")
		if blendshape_node:
			print(f"Blendshape Node: {blendshape_node}")
		
		for row in selected_rows:
			target_name = self.ui.targets_table.item(row, 1).text()
			weight_slider = self.ui.targets_table.cellWidget(row, 2)
			weight_value = weight_slider.value() if weight_slider else 0.0
			print(f"Target: {target_name}, Weight: {weight_value:.3f}")
		
		print("--------------------------------")

	# _____________________________________ EXTRACT TAB  _____________________________________

	def populate_extract_combo_box(self):
		self.ui.extract_cb.clear()
		
		# Get all blendshape nodes in the scene
		blendshape_nodes = cmds.ls(type="blendShape")
		
		# Add them to the list widget
		self.ui.extract_cb.addItems(blendshape_nodes)

	def get_selected_targets_for_extraction(self):
		"""
		Get the selected blendshape node from the combobox and the checked target names from the table
		
		Returns:
			tuple: (blendshape_node, target_names, corrective_targets) where:
				- target_names is a list of checked targets
				- corrective_targets is a list of targets that should be correctiveed (corrective checkbox checked)
		"""
		# Get the selected blendshape node from the combobox
		blendshape_node = self.ui.extract_cb.currentText()
		if not blendshape_node:
			return None, [], []
		
		# Get all checked targets from the table
		checked_targets = []
		corrective_targets = []
		
		for i in range(self.ui.extract_table.rowCount()):
			# Check if the checkbox in column 0 is checked
			checkbox_item = self.ui.extract_table.item(i, 0)
			if checkbox_item and checkbox_item.checkState() == Qt.Checked:
				# Get the target name from column 1
				target_name = self.ui.extract_table.item(i, 1).text()
				checked_targets.append(target_name)
				
				# Check if the corrective checkbox in column 3 is checked
				corrective_item = self.ui.extract_table.item(i, 3)
				if corrective_item and corrective_item.checkState() == Qt.Checked:
					corrective_targets.append(target_name)
		
		return blendshape_node, checked_targets, corrective_targets

	def extract_blendshapes(self):
		output_group = self.ui.output_group_le.text()
		if not output_group.strip() or not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', output_group):
			error("Please enter a valid Output Group name")
			return

		# Need to select a mesh first
		mesh = cmds.ls(sl=True)
		if not mesh or not obj_is_type(mesh[-1], 'mesh'):
			error('Please select a mesh first')
			return

		# Check if output group exists
		group_exists = cmds.objExists(output_group)
		
		with UndoStack("extract_blendshapes"):
			if group_exists:
				# Check if there are children under the group
				children = cmds.listRelatives(output_group, children=True)
				if children:
					# Warn user and ask if they want to continue
					result = cmds.confirmDialog(
						title='Warning',
						message=f'The group "{output_group}" already exists and contains {len(children)} object(s).\n'
								f'If you continue, all objects under this group will be deleted.\n\n'
								f'Do you want to continue?',
						button=['Yes', 'No'],
						defaultButton='No',
						cancelButton='No',
						dismissString='No'
					)
					
					if result == 'No':
						return
					
					# Delete all children
					cmds.delete(children)
			else:
				# Create the output group
				cmds.group(empty=True, name=output_group)
			
			# Extract targets
			cmds.select(clear=True)
			blendshape_node, target_names, corrective_targets = self.get_selected_targets_for_extraction()
			for target_name in target_names:
				# Additionally, filter out target_name. eg "eye", 'look' in target_name
				cmds.select(clear=True)
				cmds.setAttr(f"{blendshape_node}.{target_name}", 1)
				dup = cmds.duplicate(mesh)[0]
				cmds.setAttr(f"{blendshape_node}.{target_name}", 0)
				dup = cmds.parent(dup, output_group)[0]
				dup = cmds.rename(dup, target_name)
				cmds.sets(dup, e=True, forceElement='initialShadingGroup')  # Assign default material
			
			# Restore selections
			cmds.select(mesh, replace=True)

	def setup_extract_tab(self):
		"""Setup the extract tab UI elements"""
		# Set up the table widget columns
		self.ui.extract_table.setColumnCount(4)
		self.ui.extract_table.setHorizontalHeaderLabels(["", "Target", "Weight", "Corrective"])
		self.ui.extract_table.verticalHeader().setMaximumSectionSize(EXTRACT_TABLE_ROW_HEIGHT)
		
		# Hide vertical header (row numbers)
		self.ui.extract_table.verticalHeader().setVisible(False)
		
		# Configure the table widget header
		header = self.ui.extract_table.horizontalHeader()
		header.setSectionResizeMode(0, QtWidgets.QHeaderView.Fixed)    # Checkbox column
		header.setSectionResizeMode(1, QtWidgets.QHeaderView.Stretch)  # Target name column
		header.setSectionResizeMode(2, QtWidgets.QHeaderView.Fixed)    # Weight column
		header.setSectionResizeMode(3, QtWidgets.QHeaderView.Fixed)    # corrective checkbox column
		
		# Set fixed width for columns
		header.resizeSection(0, 24)   # Checkbox column width
		header.resizeSection(2, 80)   # Weight column width
		header.resizeSection(3, 60)   # corrective checkbox column width
		
		# Set selection behavior to select entire rows
		self.ui.extract_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
		self.ui.extract_table.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
		
		# Apply stylesheet to make checkboxes more visible
		self.ui.extract_table.setStyleSheet("""
			QTableView::indicator {
			}
			QTableView::indicator:checked {
			}
			QTableView::indicator:unchecked {
				background-color: black;
			}
		""")

		# Setup context menu for extract table
		self.ui.extract_table.setContextMenuPolicy(Qt.CustomContextMenu)
		self.ui.extract_table.customContextMenuRequested.connect(self.show_extract_table_context_menu)

		# Connect the combobox change signal
		self.ui.extract_cb.currentIndexChanged.connect(self.populate_extract_table)

	def show_extract_table_context_menu(self, position):
		"""Show the context menu for extract table"""
		context_menu = QtWidgets.QMenu(self)
		
		check_action = QAction("Check Selected", self)
		check_action.triggered.connect(lambda: self.set_extract_rows_checked(Qt.Checked))
		context_menu.addAction(check_action)
		
		uncheck_action = QAction("Uncheck Selected", self)
		uncheck_action.triggered.connect(lambda: self.set_extract_rows_checked(Qt.Unchecked))
		context_menu.addAction(uncheck_action)
		
		context_menu.exec_(QCursor.pos())
		
	def set_extract_rows_checked(self, check_state):
		"""Set the check state of selected rows in extract table"""
		selected_rows = set()
		for index in self.ui.extract_table.selectedIndexes():
			selected_rows.add(index.row())
		
		for row in selected_rows:
			# Set extract checkbox (column 0)
			extract_item = self.ui.extract_table.item(row, 0)
			if extract_item:
				extract_item.setCheckState(check_state)

	def populate_extract_table(self):
		"""Populate the extract table with blendshape targets from the selected blendshape node"""
		# Clear the table widget
		self.ui.extract_table.clearContents()
		self.ui.extract_table.setRowCount(0)
		
		# Get the selected blendshape node from the combobox
		blendshape_node = self.ui.extract_cb.currentText()
		if not blendshape_node:
			return
		
		# Get all targets for the selected blendshape node
		targets = cmds.listAttr(blendshape_node + '.w', multi=True)
		if not targets:
			return
		
		# Set the row count
		self.ui.extract_table.setRowCount(len(targets))
		
		# Add targets to the table widget
		for i, target in enumerate(targets):
			# Column 0: Extract checkbox (checked by default)
			extract_item = QtWidgets.QTableWidgetItem()
			extract_item.setCheckState(Qt.Checked)
			extract_item.setFlags(extract_item.flags() & ~Qt.ItemIsEditable)
			self.ui.extract_table.setItem(i, 0, extract_item)
			
			# Column 1: Target name
			name_item = QtWidgets.QTableWidgetItem(target)
			name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)  # Make read-only
			self.ui.extract_table.setItem(i, 1, name_item)
			
			# Column 2: Weight value
			weight = cmds.getAttr(f"{blendshape_node}.{target}")
			weight_item = QTableWidgetItem(f"{weight:.3f}")
			# Highlight rows with non-zero weight values
			if weight > 0.0:
				weight_item.setBackground(QColor(200, 70, 30))
			weight_item.setFlags(weight_item.flags() & ~Qt.ItemIsEditable)  # Make read-only
			self.ui.extract_table.setItem(i, 2, weight_item)
			
			# Column 3: corrective checkbox (unchecked by default)
			corrective_item = QtWidgets.QTableWidgetItem()
			corrective_item.setCheckState(Qt.Unchecked)
			corrective_item.setFlags(corrective_item.flags() & ~Qt.ItemIsEditable)
			self.ui.extract_table.setItem(i, 3, corrective_item)
			
			# Set Row Height
			self.ui.extract_table.setRowHeight(i, EXTRACT_TABLE_ROW_HEIGHT)

	# _____________________________________ MAYA CALLBACK  _____________________________________

	def on_new_scene(self, *args):
		"""Handle new scene creation in Maya"""
		try:
			# Check if UI is still valid (prevents errors if UI is closed)
			self.ui.blendshape_nodes_list.count()
		except:
			return
		
		# Clear the targets table
		self.clear_targets_table()
		
		# Refresh the blendshape nodes list
		self.populate_viewer_list()
		
		# Check which tab is currently active and update accordingly
		current_tab_index = self.ui.tabs.currentIndex()
		current_tab_name = self.ui.tabs.tabText(current_tab_index)
		
		# Update tab-specific content based on which tab is active
		if current_tab_name == "Node Viewer":
			self.populate_viewer_list()
		elif current_tab_name == "Extract":
			self.populate_extract_combo_box()

	def refresh_blendshape_list(self, *args):
		"""Handle Maya selection changes"""
		try:
			# Check if UI is still valid (prevents errors if UI is closed)
			self.ui.blendshape_nodes_list.count()
		except:
			return

		selections = cmds.ls(selection=True)
		if selections and obj_is_type(selections[-1], 'mesh'):
			
			# Check if any selected object is a blendshape node
			# blendshape_nodes = [node for node in selection if cmds.nodeType(node) == "blendShape"]
			blendshape_nodes = get_blendshape_nodes(selections[-1])  # Get blendshape nodes from selection
			if blendshape_nodes:
				# Check which tab is currently active and update accordingly
				current_tab_index = self.ui.tabs.currentIndex()
				current_tab_name = self.ui.tabs.tabText(current_tab_index)
				
				# Update tab-specific content based on which tab is active
				if current_tab_name == "Node Viewer":
					# Find and select the item in the list widget
					for i in range(self.ui.blendshape_nodes_list.count()):
						item = self.ui.blendshape_nodes_list.item(i)
						if item.text() == blendshape_nodes[0]:
							self.ui.blendshape_nodes_list.setCurrentItem(item)
							return

				elif current_tab_name == "Extract":
					# Check if the blendshape node exists in the combobox
					index = self.ui.extract_cb.findText(blendshape_nodes[0])
					if index >= 0:
						# Item exists, set it as current
						self.ui.extract_cb.setCurrentIndex(index)
					else:
						# Item doesn't exist, add it first
						self.ui.extract_cb.addItem(blendshape_nodes[0])
						self.ui.extract_cb.setCurrentText(blendshape_nodes[0])

	# _____________________________________ EVENTS  _____________________________________

	def table_key_press_event(self, event):
		"""Handle key press events for the targets table"""
		# Check for Ctrl+Up/Down to move selected rows
		# if event.modifiers() == Qt.ControlModifier:
		# 	if event.key() == Qt.Key_Up:
		# 		self.move_selected_rows(-1)  # Move up
		# 		return
		# 	elif event.key() == Qt.Key_Down:
		# 		self.move_selected_rows(1)   # Move down
		# 		return
		
		# Call the original keyPressEvent for other keys
		QtWidgets.QTableWidget.keyPressEvent(self.ui.targets_table, event)

	def eventFilter(self, obj, event):
		"""Handle middle-click on blendshape nodes list"""
		if obj == self.ui.blendshape_nodes_list.viewport() and event.type() == QEvent.MouseButtonPress:
			if event.button() == Qt.MiddleButton:
				# Get item at position
				item = self.ui.blendshape_nodes_list.itemAt(event.pos())
				if item:
					# Select the item in the list widget
					self.ui.blendshape_nodes_list.setCurrentItem(item)
					blendshape_node = item.text()
					select_blendshape_mesh(blendshape_node)
					return True
		return super().eventFilter(obj, event)

	def close(self):
		self.callback_manager.removeAllManagedCB()
		self.deleteLater()

	def closeEvent(self, event):
		self.close()

	def dockCloseEventTriggered(self):
		self.close()


global blendshapesEditor_app

workspace_controls = cmds.lsUI(type='workspaceControl')
for control in workspace_controls:
	if control.startswith('BlendshapesEditor'):
		cmds.workspaceControl(control, e=True, close=True)
		cmds.deleteUI(control, control=True)

try:
	blendshapesEditor_app.deleteLater()
except Exception:
	pass

blendshapesEditor_app = BlendshapesEditor_Window()
