import re
import os
from pathlib import Path
from stat import S_IREAD, S_IRGRP, S_IROTH, S_IWUSR

import maya.mel as mel
import maya.cmds as cmds

from .clipboard import get_filepaths_from_clipboard
from .utils import parse_hashes_numbers

def reload_current_scene():
	current_maya_file_path = cmds.file(q=True, sn=True)
	if current_maya_file_path:
		cmds.file(current_maya_file_path, o=True, force=True)

def lock_current_scene():
	current_maya_file_path = cmds.file(q=True, sn=True)
	if current_maya_file_path:
		os.chmod(current_maya_file_path, S_IREAD|S_IRGRP|S_IROTH)

# Unlock current scene
def unlock_current_scene():
	current_maya_file_path = cmds.file(q=True, sn=True)
	if current_maya_file_path:
		os.chmod(current_maya_file_path, S_IWUSR|S_IREAD) # This makes the file read/write for the owner

def open_scene_folder():
	current_maya_file_path = cmds.file(q=True, sceneName=True)

	if current_maya_file_path and os.path.isfile(current_maya_file_path):
		this_maya_file_dir = os.path.dirname(current_maya_file_path)
		os.startfile(this_maya_file_dir)

def open_file_from_clipboard():
	files = get_filepaths_from_clipboard()
	if isinstance(files, list) and len(files) >= 1:
		file_to_open = files[0]
		filepath_noext, ext = os.path.splitext(file_to_open)
		ext_map = {".ma": "mayaAscii", ".mb": "mayaBinary"}
		if os.path.exists(file_to_open):
			if ext == ".ma" or ext == ".mb":
				cmds.file(file_to_open, open=True, force=True)
				mel.eval('addRecentFile("{}", "{}")'.format(file_to_open, ext_map[ext]))
			elif ext == ".fbx":
				cmds.file(
					file_to_open, i=True, mergeNamespacesOnClash=True, groupReference=False
				)

def save_as_new_version():
	"""Save current Maya scene as a new version (increments version number)."""
	current_file = cmds.file(query=True, sceneName=True)
	if not current_file:
		return
	current_file = Path(current_file)

	# Determine file type if saving existing file
	if current_file.exists():
		file_type = "mayaAscii" if current_file.suffix == ".ma" else "mayaBinary"
	else:
		# Default to mayaAscii for new scenes
		file_type = "mayaAscii"
	
	# Extract version information
	stem = current_file.stem
	new_stem = parse_hashes_numbers(stem, 2)

	if new_stem == stem:
		new_stem = f"{stem}_v001"

	# Construct new path
	new_file = current_file.with_stem(new_stem)

	# Check if the new file already exists
	if new_file.exists():
		cmds.error(f"File already exists: {new_file}")
		return

	# Save the file
	cmds.file(rename=str(new_file))
	cmds.file(save=True, type=file_type)

	print(f"Saved new version as: {new_file}")
