# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'blendshapesEditor.ui'
##
## Created by: Qt User Interface Compiler version 6.5.3
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QAction, QBrush, QColor, QConicalGradient,
    QCursor, QFont, QFontDatabase, QGradient,
    QIcon, QImage, QKeySequence, QLinearGradient,
    QPainter, QPalette, QPixmap, QRadialGradient,
    QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QHBoxLayout, QHeaderView,
    QLabel, QLineEdit, QListWidget, QListWidgetItem,
    QMainWindow, QPushButton, QSizePolicy, QSplitter,
    QTabWidget, QTableWidget, QTableWidgetItem, QVBoxLayout,
    QWidget)

from .customStatusBar import CustomStatusBar
from .mytablewidget import MyTableWidget

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(354, 527)
        self.actionCreate_Character_Visibility_Set = QAction(MainWindow)
        self.actionCreate_Character_Visibility_Set.setObjectName(u"actionCreate_Character_Visibility_Set")
        self.export_visibility_setup_action = QAction(MainWindow)
        self.export_visibility_setup_action.setObjectName(u"export_visibility_setup_action")
        self.import_visibility_setup_action = QAction(MainWindow)
        self.import_visibility_setup_action.setObjectName(u"import_visibility_setup_action")
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout = QVBoxLayout(self.centralwidget)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.tabs = QTabWidget(self.centralwidget)
        self.tabs.setObjectName(u"tabs")
        self.individual_objects_tab = QWidget()
        self.individual_objects_tab.setObjectName(u"individual_objects_tab")
        self.horizontalLayout_7 = QHBoxLayout(self.individual_objects_tab)
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.splitter = QSplitter(self.individual_objects_tab)
        self.splitter.setObjectName(u"splitter")
        self.splitter.setOrientation(Qt.Horizontal)
        self.widget = QWidget(self.splitter)
        self.widget.setObjectName(u"widget")
        self.horizontalLayout = QHBoxLayout(self.widget)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_4 = QVBoxLayout()
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.label_4 = QLabel(self.widget)
        self.label_4.setObjectName(u"label_4")

        self.horizontalLayout_5.addWidget(self.label_4)

        self.blendshape_nodes_filter_le = QLineEdit(self.widget)
        self.blendshape_nodes_filter_le.setObjectName(u"blendshape_nodes_filter_le")

        self.horizontalLayout_5.addWidget(self.blendshape_nodes_filter_le)


        self.verticalLayout_4.addLayout(self.horizontalLayout_5)

        self.blendshape_nodes_list = QListWidget(self.widget)
        self.blendshape_nodes_list.setObjectName(u"blendshape_nodes_list")

        self.verticalLayout_4.addWidget(self.blendshape_nodes_list)


        self.horizontalLayout.addLayout(self.verticalLayout_4)

        self.splitter.addWidget(self.widget)
        self.widget_2 = QWidget(self.splitter)
        self.widget_2.setObjectName(u"widget_2")
        self.horizontalLayout_6 = QHBoxLayout(self.widget_2)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2 = QVBoxLayout()
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.label_2 = QLabel(self.widget_2)
        self.label_2.setObjectName(u"label_2")

        self.horizontalLayout_3.addWidget(self.label_2)

        self.targets_filter_le = QLineEdit(self.widget_2)
        self.targets_filter_le.setObjectName(u"targets_filter_le")

        self.horizontalLayout_3.addWidget(self.targets_filter_le)


        self.verticalLayout_2.addLayout(self.horizontalLayout_3)

        self.targets_table = MyTableWidget(self.widget_2)
        self.targets_table.setObjectName(u"targets_table")

        self.verticalLayout_2.addWidget(self.targets_table)


        self.horizontalLayout_6.addLayout(self.verticalLayout_2)

        self.splitter.addWidget(self.widget_2)

        self.horizontalLayout_7.addWidget(self.splitter)

        self.tabs.addTab(self.individual_objects_tab, "")
        self.tab = QWidget()
        self.tab.setObjectName(u"tab")
        self.verticalLayout_3 = QVBoxLayout(self.tab)
        self.verticalLayout_3.setSpacing(3)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.verticalLayout_3.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.label = QLabel(self.tab)
        self.label.setObjectName(u"label")
        sizePolicy = QSizePolicy(QSizePolicy.Maximum, QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)

        self.horizontalLayout_2.addWidget(self.label)

        self.extract_cb = QComboBox(self.tab)
        self.extract_cb.setObjectName(u"extract_cb")

        self.horizontalLayout_2.addWidget(self.extract_cb)


        self.verticalLayout_3.addLayout(self.horizontalLayout_2)

        self.extract_table = QTableWidget(self.tab)
        self.extract_table.setObjectName(u"extract_table")

        self.verticalLayout_3.addWidget(self.extract_table)

        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.label_3 = QLabel(self.tab)
        self.label_3.setObjectName(u"label_3")

        self.horizontalLayout_4.addWidget(self.label_3)

        self.output_group_le = QLineEdit(self.tab)
        self.output_group_le.setObjectName(u"output_group_le")

        self.horizontalLayout_4.addWidget(self.output_group_le)

        self.set_output_group_btn = QPushButton(self.tab)
        self.set_output_group_btn.setObjectName(u"set_output_group_btn")
        self.set_output_group_btn.setMaximumSize(QSize(30, 16777215))

        self.horizontalLayout_4.addWidget(self.set_output_group_btn)


        self.verticalLayout_3.addLayout(self.horizontalLayout_4)

        self.extract_btn = QPushButton(self.tab)
        self.extract_btn.setObjectName(u"extract_btn")
        self.extract_btn.setMinimumSize(QSize(0, 30))

        self.verticalLayout_3.addWidget(self.extract_btn)

        self.tabs.addTab(self.tab, "")

        self.verticalLayout.addWidget(self.tabs)

        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = CustomStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)

        self.tabs.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"Blendshapes Editor", None))
        self.actionCreate_Character_Visibility_Set.setText(QCoreApplication.translate("MainWindow", u"Create Character_Visibility_Set", None))
        self.export_visibility_setup_action.setText(QCoreApplication.translate("MainWindow", u"Export Visibility Setup", None))
        self.import_visibility_setup_action.setText(QCoreApplication.translate("MainWindow", u"Import Visibility Setup", None))
        self.label_4.setText(QCoreApplication.translate("MainWindow", u"Blendshape Nodes : ", None))
        self.label_2.setText(QCoreApplication.translate("MainWindow", u"Targets : ", None))
        self.tabs.setTabText(self.tabs.indexOf(self.individual_objects_tab), QCoreApplication.translate("MainWindow", u"Node Viewer", None))
        self.label.setText(QCoreApplication.translate("MainWindow", u"Blendshape Nodes : ", None))
        self.label_3.setText(QCoreApplication.translate("MainWindow", u"Output Group :", None))
        self.set_output_group_btn.setText(QCoreApplication.translate("MainWindow", u"<", None))
        self.extract_btn.setText(QCoreApplication.translate("MainWindow", u"Extract", None))
        self.tabs.setTabText(self.tabs.indexOf(self.tab), QCoreApplication.translate("MainWindow", u"Extract", None))
    # retranslateUi

