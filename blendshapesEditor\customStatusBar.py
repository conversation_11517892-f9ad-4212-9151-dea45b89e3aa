from PySide6.QtWidgets import QStatusBar, QLabel
from PySide6.QtCore import QTimer

class CustomStatusBar(QStatusBar):
	def __init__(self, parent=None):
		super().__init__(parent)

		# Create separate labels for different message types
		self.normal_label = QLabel()
		self.warning_label = QLabel()
		self.error_label = QLabel()

		# Add labels to status bar
		self.addWidget(self.normal_label, 1)
		self.addWidget(self.warning_label, 1)
		self.addWidget(self.error_label, 1)

		# Hide all by default
		self.normal_label.hide()
		self.warning_label.hide()
		self.error_label.hide()

		# Apply styles
		self.normal_label.setStyleSheet("""
            /*background-color: #e6f2ff;*/
            background-color: #b5bdc7;
            color: #0066cc;
            padding: 3px;
            border-radius: 3px;
        """)

		self.warning_label.setStyleSheet("""
            background-color: #fff9e6;
            color: #cc7700;
            padding: 3px;
            border-radius: 3px;
        """)

		self.error_label.setStyleSheet("""
            background-color: #ffe6e6;
            color: #cc0000;
            padding: 3px;
            border-radius: 3px;
        """)

	def showNormalMessage(self, title, message, duration=0):
		self._hideAllMessages()
		self.normal_label.setText(f"{title}: {message}")
		self.normal_label.show()
		if duration > 0:
			QTimer.singleShot(duration, self.normal_label.hide)

	def showWarningMessage(self, title, message, duration=0):
		self._hideAllMessages()
		self.warning_label.setText(f"{title}: {message}")
		self.warning_label.show()
		if duration > 0:
			QTimer.singleShot(duration, self.warning_label.hide)

	def showErrorMessage(self, title, message, duration=0):
		self._hideAllMessages()
		self.error_label.setText(f"{title}: {message}")
		self.error_label.show()
		if duration > 0:
			QTimer.singleShot(duration, self.error_label.hide)

	def _hideAllMessages(self):
		self.normal_label.hide()
		self.warning_label.hide()
		self.error_label.hide()
