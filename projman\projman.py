
import os
import configparser

import pymel.core as pm
import maya.cmds as cmds
import maya.OpenMayaUI as omui

from shiboken6 import wrapInstance
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Signal as pyqtSignal

import importlib
import RIn.projman.projman_ui as projman_ui
importlib.reload(projman_ui)

try:
	THIS_FILE_DIR = os.path.split(os.path.realpath(__file__))[0]
except NameError:
	cmds.error('Please open up main.py to edit THIS_FILE_DIR.')
	THIS_FILE_DIR = 'C:\\Users\\<USER>\\Documents\\maya\\201x-x64\\scripts\\____APPNAME____\\'

#  Global variables
#----------------------------------------
RECENT_PATHS_SIZE = 7
UI_FILE = os.path.join(THIS_FILE_DIR, 'projman.ui')
SETTINGS_FILE = os.path.join(THIS_FILE_DIR, 'projman_settings.ini')

#  QT Window
#----------------------------------------
def get_main_window():
	return wrapInstance(int(omui.MQtUtil.mainWindow()), QtWidgets.QWidget)


# Load settings
Config = configparser.ConfigParser()
Config.read(SETTINGS_FILE)
RECENT_PATHS_SIZE = Config.getint('ProjMan_Settings', 'recent_paths_size')

def f7(seq):
	""" # http://stackoverflow.com/questions/480214/how-do-you-remove-duplicates-from-a-list-in-python-whilst-preserving-order
	"""
	seen = set()
	seen_add = seen.add
	return [ x for x in seq if x not in seen and not seen_add(x)]

def get_recent_projects():
	""" get_recent_projects() -> return a list if sucess else return None
	"""
	recent_projects_txt = os.path.join(THIS_FILE_DIR, 'projman_recent_projects.txt')

	lines = []
	if os.path.exists(recent_projects_txt):
		FILE = open(recent_projects_txt, 'r')
		lines = FILE.readlines()
		FILE.close()
	
	# Remove empty lines and do some cleanups
	lines = [line.replace('\r', '') for line in lines]
	lines = [line.replace('\n', '') for line in lines]
	lines = [line for line in lines if len(line) >= 2]
	lines = [os.path.normpath(line) for line in lines]
	lines = [line for line in lines if os.path.isdir(line)]
	
	if len(lines) < 1:
		return None
	
	# Remove duplicates and return the list
	return f7(lines)[:RECENT_PATHS_SIZE]

def append_to_recent_paths(path):
	if not os.path.isdir(path):
		return False
	
	recent_paths = get_recent_projects() or []
	
	if path not in recent_paths:
		recent_paths.insert(0, path)
	
	if path in recent_paths:
		recent_paths.remove(path)
		recent_paths.insert( 0, path )
	
	# Keep only x amount of recent paths, the top one is the most recent
	recent_paths = recent_paths[:RECENT_PATHS_SIZE]
	str = '\n'.join(recent_paths)
	
	recent_paths_filepath = os.path.split(os.path.realpath(__file__))[0] + '\\projman_recent_projects.txt'
	FILE = open(recent_paths_filepath, 'w')
	FILE.write(str)
	FILE.close()

class ProjMan_Window(QtWidgets.QMainWindow, projman_ui.Ui_MainWindow):
	def __init__(self, parent=get_main_window()):
		super().__init__(parent)
		
		proj_dir = cmds.workspace(query=1, rootDirectory=1)
		recent_paths = get_recent_projects()
		
		self._project_dir = proj_dir
		
		# SetupUi() makes widget available to self
		self.setupUi(self)
		
		# Connect signals and slots
		self.openProjectDir_QButton.clicked.connect(self.on_open_project_dir)
		self.changeProject_QButton.clicked.connect(self.on_add_project)
		self.autoSetProject_QButton.clicked.connect(self.on_auto_set_project)
		self.projects_QList.itemDoubleClicked.connect(self.on_set_project)
		# self.projects_QList.itemClicked.connect(self.on_fillExportName)
		# self.projects_QList.connect(self.projects_QList,QtCore.SIGNAL("customContextMenuRequested(QPoint)" ), self.on_listRightClick)

		# # Connect signals and slots
		# self.projects_QList.itemDoubleClicked.connect(self.on_importItem)
		# self.projects_QList.itemClicked.connect(self.on_fillExportName)
		# self.projects_QList.connect(self.projects_QList,QtCore.SIGNAL("customContextMenuRequested(QPoint)" ), self.on_listRightClick)
		# self.favExports_listWidget.itemDoubleClicked.connect(self.on_exportFavourite)
		# self.refreshlist_buttonWidget.clicked.connect(self.refresh_project_list)
		# self.importSelected_buttonWidget.clicked.connect(self.on_importSelectedItems)
		# self.opendir_buttonWidget.clicked.connect(self.on_openDir)
		# self.setdir_buttonWidget.clicked.connect(self.on_add_project)
		# self.setname_buttonWidget.clicked.connect(self.on_setName)
		# self.exportSelected_buttonWidget.clicked.connect(self.on_exportSelected)
		# self.switch_buttonWidget.clicked.connect(self.on_toggleFav)
		# self.filter_lineWidget.textChanged.connect(self.refresh_project_list)
		# self.sortby_comboBox.currentIndexChanged.connect(self.refresh_project_list)
		
		# # Keyboard operations
		remove_project_from_list_Action = self.createAction('Delete obj file', self.on_remove_project_from_list, "Delete")
		self.projects_QList.addAction(remove_project_from_list_Action)
		# openObjFile_Action = self.createAction('Open obj file', self.on_openObjFile, "O")
		# self.projects_QList.addAction(openObjFile_Action)
		# deleteFav_Action = self.createAction('Delete fav', self.on_deleteFav, "Delete")
		# self.favExports_listWidget.addAction(deleteFav_Action)
		# lineEnterFile_Action = self.createAction('Line Enter', self.on_lineEnter, "Enter")
		# self.objsdir_lineWidget.addAction(lineEnterFile_Action)
		# self.objsdir_lineWidget.addAction(lineEnterFile_Action)
		
		# # Main menu
		# self.settings_Menu.aboutToShow.connect(self.on_openSettingsMenu)
		
		# # Popup menu
		# self.objsdir_popMenu = QtWidgets.QMenu(self)
		# self.objsdir_lineWidget.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
		# self.objsdir_lineWidget.customContextMenuRequested.connect(self.popup_objsdirLineEdit)
		
		# self.exportbtn_popMenu = QtWidgets.QMenu(self)
		# self.exportSelected_buttonWidget.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
		# self.exportSelected_buttonWidget.customContextMenuRequested.connect(self.popup_exportBtn)
		
		# exportAndOpen_Action = QtGui.QAction('Export and Open', self)
		# self.connect(exportAndOpen_Action, QtCore.SIGNAL("triggered()"), partial(self.on_exportSelected, True))
		# self.exportbtn_popMenu.addAction(exportAndOpen_Action)
		
		# Tests area
		pass # no tests for now...
		
		# Other inits
		self.currentProject_QLabel.setText(proj_dir)
		# self.projects_QList.setFocus()

		# Populate projects list
		self.refresh_project_list()

		self.show()
	
	def on_lineEnter(self):
		newpath = self.objsdir_lineWidget.text()
		if os.path.isdir(newpath):
			self._project_dir = newpath
			append_to_recent_paths(newpath)
			self.refresh_project_list()
		else:
			self.objsdir_lineWidget.setText(self._project_dir)
	
	def on_listRightClick(self, QPos):
		self.listMenu= QtWidgets.QMenu()
		menu_item = self.listMenu.addAction("Add to Fav")
		self.connect(menu_item, QtCore.SIGNAL("triggered()"), self.on_addToFav) 
		parentPosition = self.projects_QList.mapToGlobal(QtCore.QPoint(0, 0))        
		self.listMenu.move(parentPosition + QPos)
		self.listMenu.show()
	
	def popup_exportBtn(self, point):
		self.exportbtn_popMenu.exec_(self.exportSelected_buttonWidget.mapToGlobal(point))
	
	def refresh_project_list(self):
		self.projects_QList.clear()
		for path in get_recent_projects():
			self.projects_QList.insertItem(self.projects_QList.count(), path)

	def add_project(self, path):
		# Add to project list
		if os.path.isdir(path):
			self._project_dir = path
			self.currentProject_QLabel.setText(path)
			append_to_recent_paths(path)
			self.refresh_project_list()

	def on_add_project(self):
		proj_dir = cmds.workspace(query=1, rootDirectory=1)
		startingdir = self._project_dir if os.path.isdir(self._project_dir) else proj_dir
		path = pm.fileDialog2(fileMode=3, okCaption='OK', dir=startingdir)   # returns a 'str'
		if path:
			path = path[0]
			self.add_project(path)
			self.set_project(path)

	def set_project(self, path):
		cmds.workspace(path, openWorkspace=True)
		proj_dir = cmds.workspace(query=1, rootDirectory=1)
		self.currentProject_QLabel.setText(proj_dir)
		print("Project Set to: {}".format(proj_dir))
		cmds.confirmDialog(title="Project Set to", message=proj_dir)

	def on_set_project(self):
		selecteditem = str(self.projects_QList.currentItem().text())
		append_to_recent_paths(selecteditem)
		self.set_project(selecteditem)
		self.refresh_project_list()

	def on_remove_project_from_list(self):
		selected_items = self.projects_QList.selectedItems()
		for sel_item in selected_items:
			sel_item = str(sel_item.text())
		self.refresh_project_list()

	def on_auto_set_project(self):
		pass

	def on_open_project_dir(self):
		if os.path.isdir(self._project_dir):
			path = "\"{}\"".format(self._project_dir.replace( '/', '\\' ))
			print (path)
			os.startfile(path)
			# somehow the followings not working, need to investigate
			# subprocess.call(['open ', path])
			# subprocess.Popen(['explorer', str(self._project_dir).replace( '/', '\\' )])
	
	def on_setName(self):
		sels = pm.selected()
		if len(sels) > 0:
			firstsel = sels[0]
			self.objname_lineWidget.setText(firstsel.nodeName())
	
	def on_fillExportName(self, item):
		selecteditem = str(item.text()).rsplit('.', 1)[0]
		self.objname_lineWidget.setText(selecteditem)
	
	def createAction(self, text, slot=None, shortcut=None, icon=None, tip=None, checkable=False, signal="triggered()"):
		# ( Copied from Rapid GUI Programming with Python and Qt,
		#		Chapter 6, imagechanger.py )
		action = QtGui.QAction( text, self )
		if icon != None:
			action.setIcon(QIcon(":/%s.png" % icon))
		if shortcut != None:
			action.setShortcut( shortcut )
		if tip != None:
			action.setToolTip( tip )
			action.setStatusTip( tip )
		if slot != None:
			self.connect( action, QtCore.SIGNAL(signal), slot )
		if checkable:
			action.setCheckable( True )
		return action

global projman_app
try:
	projman_app.deleteLater()
except:
	pass
projman_app = ProjMan_Window()
