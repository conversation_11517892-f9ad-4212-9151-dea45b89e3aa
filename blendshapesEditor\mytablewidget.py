from PySide6.QtWidgets import QTableWidget, QHeaderView
from PySide6.QtCore import Qt


class CustomHeaderView(QHeaderView):
	def __init__(self, orientation, parent=None):
		super().__init__(orientation, parent)
		self.table = parent
		self.table.verticalHeader().setVisible(False)
	
	def mousePressEvent(self, event):
		if event.button() == Qt.MiddleButton:
			# Toggle vertical header visibility
			if self.table:
				is_visible = self.table.verticalHeader().isVisible()
				self.table.verticalHeader().setVisible(not is_visible)
			return
		super().mousePressEvent(event)


class MyTableWidget(QTableWidget):
	def __init__(self, parent=None):
		super().__init__(parent)
		
		# Replace the horizontal header with our custom one
		custom_header = CustomHeaderView(Qt.Horizontal, self)
		self.setHorizontalHeader(custom_header)
