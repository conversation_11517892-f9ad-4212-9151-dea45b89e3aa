<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>350</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ProjMan</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QLabel" name="label1">
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;Current Project&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
      <property name="textFormat">
       <enum>Qt::RichText</enum>
      </property>
     </widget>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QLabel" name="currentProject_QLabel">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>20</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">color: yellow;
height: 30px</string>
        </property>
        <property name="text">
         <string>TextLabel</string>
        </property>
        <property name="textInteractionFlags">
         <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByMouse</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="openProjectDir_QButton">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>45</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="text">
         <string>Open</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QPushButton" name="changeProject_QButton">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>30</height>
       </size>
      </property>
      <property name="text">
       <string>Change Project</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QListWidget" name="projects_QList">
      <property name="contextMenuPolicy">
       <enum>Qt::CustomContextMenu</enum>
      </property>
      <property name="toolTip">
       <string>Objs inside the folder</string>
      </property>
      <property name="selectionMode">
       <enum>QAbstractItemView::ExtendedSelection</enum>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="setProject_QButton">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>30</height>
       </size>
      </property>
      <property name="text">
       <string>Set</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="autoSetProject_QButton">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>30</height>
       </size>
      </property>
      <property name="text">
       <string>Auto Set Project</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
