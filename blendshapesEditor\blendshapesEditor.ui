<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>354</width>
    <height>527</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Blendshapes Editor</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QTabWidget" name="tabs">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="individual_objects_tab">
       <attribute name="title">
        <string>Node Viewer</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_7">
        <item>
         <widget class="QSplitter" name="splitter">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <widget class="QWidget" name="widget" native="true">
           <layout class="QHBoxLayout" name="horizontalLayout">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_4">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_5">
                <item>
                 <widget class="QLabel" name="label_4">
                  <property name="text">
                   <string>Blendshape Nodes : </string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="blendshape_nodes_filter_le"/>
                </item>
               </layout>
              </item>
              <item>
               <widget class="QListWidget" name="blendshape_nodes_list"/>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="widget_2" native="true">
           <layout class="QHBoxLayout" name="horizontalLayout_6">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_2">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_3">
                <item>
                 <widget class="QLabel" name="label_2">
                  <property name="text">
                   <string>Targets : </string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="targets_filter_le"/>
                </item>
               </layout>
              </item>
              <item>
               <widget class="MyTableWidget" name="targets_table"/>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab">
       <attribute name="title">
        <string>Extract</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <property name="spacing">
         <number>3</number>
        </property>
        <property name="leftMargin">
         <number>3</number>
        </property>
        <property name="topMargin">
         <number>3</number>
        </property>
        <property name="rightMargin">
         <number>3</number>
        </property>
        <property name="bottomMargin">
         <number>3</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <widget class="QLabel" name="label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>Blendshape Nodes : </string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="extract_cb"/>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QTableWidget" name="extract_table"/>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <item>
           <widget class="QLabel" name="label_3">
            <property name="text">
             <string>Output Group :</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="output_group_le"/>
          </item>
          <item>
           <widget class="QPushButton" name="set_output_group_btn">
            <property name="maximumSize">
             <size>
              <width>30</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>&lt;</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QPushButton" name="extract_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="text">
           <string>Extract</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="CustomStatusBar" name="statusbar"/>
  <action name="actionCreate_Character_Visibility_Set">
   <property name="text">
    <string>Create Character_Visibility_Set</string>
   </property>
  </action>
  <action name="export_visibility_setup_action">
   <property name="text">
    <string>Export Visibility Setup</string>
   </property>
  </action>
  <action name="import_visibility_setup_action">
   <property name="text">
    <string>Import Visibility Setup</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CustomStatusBar</class>
   <extends>QStatusBar</extends>
   <header>.customStatusBar.h</header>
  </customwidget>
  <customwidget>
   <class>MyTableWidget</class>
   <extends>QTableWidget</extends>
   <header>.mytablewidget.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
