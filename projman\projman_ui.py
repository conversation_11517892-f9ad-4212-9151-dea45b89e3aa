# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'projman.ui'
##
## Created by: Qt User Interface Compiler version 6.5.3
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON><PERSON><PERSON>, Q<PERSON><PERSON>r, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>, QFontData<PERSON>, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractItemView, QApplication, QHBoxLayout, QLabel,
    QListWidget, QListWidgetItem, QMainWindow, QPushButton,
    QSizePolicy, QVBoxLayout, QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(350, 300)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout = QVBoxLayout(self.centralwidget)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.label1 = QLabel(self.centralwidget)
        self.label1.setObjectName(u"label1")
        self.label1.setTextFormat(Qt.RichText)

        self.verticalLayout.addWidget(self.label1)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.currentProject_QLabel = QLabel(self.centralwidget)
        self.currentProject_QLabel.setObjectName(u"currentProject_QLabel")
        self.currentProject_QLabel.setMinimumSize(QSize(0, 20))
        self.currentProject_QLabel.setStyleSheet(u"color: yellow;\n"
"height: 30px")
        self.currentProject_QLabel.setTextInteractionFlags(Qt.LinksAccessibleByMouse|Qt.TextSelectableByMouse)

        self.horizontalLayout.addWidget(self.currentProject_QLabel)

        self.openProjectDir_QButton = QPushButton(self.centralwidget)
        self.openProjectDir_QButton.setObjectName(u"openProjectDir_QButton")
        self.openProjectDir_QButton.setMinimumSize(QSize(0, 30))
        self.openProjectDir_QButton.setMaximumSize(QSize(45, 16777215))

        self.horizontalLayout.addWidget(self.openProjectDir_QButton)


        self.verticalLayout.addLayout(self.horizontalLayout)

        self.changeProject_QButton = QPushButton(self.centralwidget)
        self.changeProject_QButton.setObjectName(u"changeProject_QButton")
        self.changeProject_QButton.setMinimumSize(QSize(0, 30))

        self.verticalLayout.addWidget(self.changeProject_QButton)

        self.projects_QList = QListWidget(self.centralwidget)
        self.projects_QList.setObjectName(u"projects_QList")
        self.projects_QList.setContextMenuPolicy(Qt.CustomContextMenu)
        self.projects_QList.setSelectionMode(QAbstractItemView.ExtendedSelection)

        self.verticalLayout.addWidget(self.projects_QList)

        self.setProject_QButton = QPushButton(self.centralwidget)
        self.setProject_QButton.setObjectName(u"setProject_QButton")
        self.setProject_QButton.setMinimumSize(QSize(0, 30))

        self.verticalLayout.addWidget(self.setProject_QButton)

        self.autoSetProject_QButton = QPushButton(self.centralwidget)
        self.autoSetProject_QButton.setObjectName(u"autoSetProject_QButton")
        self.autoSetProject_QButton.setMinimumSize(QSize(0, 30))

        self.verticalLayout.addWidget(self.autoSetProject_QButton)

        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"ProjMan", None))
        self.label1.setText(QCoreApplication.translate("MainWindow", u"<html><head/><body><p><span style=\" font-weight:600;\">Current Project</span></p></body></html>", None))
        self.currentProject_QLabel.setText(QCoreApplication.translate("MainWindow", u"TextLabel", None))
        self.openProjectDir_QButton.setText(QCoreApplication.translate("MainWindow", u"Open", None))
        self.changeProject_QButton.setText(QCoreApplication.translate("MainWindow", u"Change Project", None))
#if QT_CONFIG(tooltip)
        self.projects_QList.setToolTip(QCoreApplication.translate("MainWindow", u"Objs inside the folder", None))
#endif // QT_CONFIG(tooltip)
        self.setProject_QButton.setText(QCoreApplication.translate("MainWindow", u"Set", None))
        self.autoSetProject_QButton.setText(QCoreApplication.translate("MainWindow", u"Auto Set Project", None))
    # retranslateUi

