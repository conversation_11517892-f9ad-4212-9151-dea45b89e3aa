"""
import RIn.commands
importlib.reload(RIn.commands)
RIn.commands.remove_RIn_cmds()
RIn.commands.create_RIn_cmds()
"""

from functools import partial

import maya.cmds as cmds

# Check out how to repeat commands here: 
# https://groups.google.com/g/python_inside_maya/c/xfuCYBO6aLg
# https://gist.github.com/justinfx/f86320b6d9b567e6b8e2

CATEGORY_NAME = 'RIn'
PIE_CATEGORY_NAME = 'RIn_Pie'

commands = r"""
f'''import RIn.{module};
RIn.{module}.{function}({params})'''
"""

commands_reload_ver = r"""
f'''import importlib
import RIn.{module};
importlib.reload(RIn.{module})
RIn.{module}.{function}({params})'''
"""

pie_menu_commands = r"""
f'''RIn.pieMenus.{pieModule}.PieMenu.{function}({params})'''
"""

# _____________________________________ RELEASE VERSION  _____________________________________

# commands = r"""
# f'''import RIn.{module};
# RIn.{module}.{function}()'''
# """

def create_runTimeCommand(name, cmd, ann='', cl='python', cat='RIn'):
	if cmds.runTimeCommand(name, exists=True):
		cmds.runTimeCommand(name, e=True, delete=True)
	runTimeCommand = cmds.runTimeCommand(name, ann=ann, c=cmd, cl=cl, cat=cat)
	print(f"[RIn] RunTimeCommand created: {name}")
	return runTimeCommand

def create_runTimeCmd(name='', module='', function='', params='', pieModule='', cmd='', rCmd='', ann='', cl='python', reload=False):
	if pieModule:
		function = 'show_at_mouse'
		create_runTimeCommand(name=name, cmd=eval(pie_menu_commands), cl=cl, cat=PIE_CATEGORY_NAME)
		function = 'release'
		create_runTimeCommand(name=name+'_Release', cmd=eval(pie_menu_commands), cl=cl, cat=PIE_CATEGORY_NAME)
	else:
		if module and function:
			if reload:
				cmd = eval(commands_reload_ver)
			else:
				cmd = eval(commands)
		if not name:
			name = function.replace('_', ' ').title().replace(' ', '_')
		if not ann:
			ann = name.replace('_', ' ')
		create_runTimeCommand(name, cmd, ann=ann, cl=cl, cat=CATEGORY_NAME)

def create_RIn_cmds():
	create_runTimeCmd('Object_Mode', cmd="selectMode -component;selectMode -object;", cl='mel')
	create_runTimeCmd('Combine_Mesh', cmd="cleanPolyCombine;", cl='mel')
	create_runTimeCmd('Center_Pivot', cmd="xform -cpc;", cl='mel')
	create_runTimeCmd('Reset_Pivot', cmd="resetPivot;", cl='mel')
	create_runTimeCmd('Transfer_Attributes_Options', cmd="performTransferAttributes 1;", cl='mel')
	create_runTimeCmd('Show_Material_Attribute_Editor', cmd="ShowShadingGroupAttributeEditor;", cl='mel')
	create_runTimeCmd('Toggle_Wireframe', cmd="toggleWireframeOnShadedOption;", cl='mel')
	create_runTimeCmd('Toggle_Selection_Highlighting', module="viewport", function="toggle_model_panel_option", params='"selectionHiliteDisplay"')
	create_runTimeCmd('Select_Shelf_miica', cmd='jumpToNamedShelf miica', cl='mel')
	create_runTimeCmd('Select_Shelf_Mesh', cmd='jumpToNamedShelf Mesh', cl='mel')
	create_runTimeCmd('Select_Shelf_Rig', cmd='jumpToNamedShelf Rig', cl='mel')
	create_runTimeCmd('Select_Shelf_Anim', cmd='jumpToNamedShelf Anim', cl='mel')
	create_runTimeCmd('Select_Shelf_Game', cmd='jumpToNamedShelf Game', cl='mel')
	create_runTimeCmd('Select_Shelf_Blendshapes', cmd='jumpToNamedShelf Blendshapes', cl='mel')

	# UVs
	create_runTimeCmd('UV_Grid_Options', cmd='performTextureViewGridOptions true', cl='mel')
	create_runTimeCmd('Shift_UV_Left', cmd='polyEditUV -u -1 -v 0', cl='mel')
	create_runTimeCmd('Shift_UV_Right', cmd='polyEditUV -u 1 -v 0', cl='mel')
	create_runTimeCmd('Align_UV_Avg_U', cmd='performAlignUV avgU;', cl='mel')
	create_runTimeCmd('Align_UV_Avg_V', cmd='performAlignUV avgV;', cl='mel')
	create_runTimeCmd('Align_UV_Min_U', cmd='alignUV minU;', cl='mel')
	create_runTimeCmd('Align_UV_Max_U', cmd='alignUV maxU;', cl='mel')
	create_runTimeCmd('Align_UV_Min_V', cmd='alignUV minV;', cl='mel')
	create_runTimeCmd('Align_UV_Max_V', cmd='alignUV maxV;', cl='mel')
	create_runTimeCmd('Unfold_UV_U', cmd='unfold -i 5000 -ss 0.001 -gb 0 -gmb 0.5 -pub 0 -ps 0 -oa 2 -us off', cl='mel')
	create_runTimeCmd('Unfold_UV_V', cmd='unfold -i 5000 -ss 0.001 -gb 0 -gmb 0.5 -pub 0 -ps 0 -oa 1 -us off', cl='mel')
	create_runTimeCmd('Unfold_UV', cmd='performUnfold 0', cl='mel')
	
	create_runTimeCmd(module="importExport", function="import_tmp")
	create_runTimeCmd(module="importExport", function="export_tmp")
	create_runTimeCmd(module="objects", function="delete_node")
	create_runTimeCmd(module="objects", function="zero_SRT")
	create_runTimeCmd(module="objects", function="freeze_transforms_selected")
	create_runTimeCmd(module="objects", function="toggle_symmetry_modeling_and_symmetrize_selection")
	create_runTimeCmd(module="objects", function="replace_object")
	create_runTimeCmd(module="meshes", function="quick_separate")
	create_runTimeCmd(module="blendshapes", function="create_blendshape")
	create_runTimeCmd(module="deformers", function="auto_select_paint_tool", reload=True)
	create_runTimeCmd(module="deformers", function="wrap_selected", reload=True)
	create_runTimeCmd(module="contextHotkeys", function="hotkey_A")
	create_runTimeCmd(module="contextHotkeys", function="context_Duplicate")
	create_runTimeCmd(module="contextHotkeys", function="context_Delete")
	create_runTimeCmd(module="contextHotkeys", function="context_Alt_C")
	create_runTimeCmd(module="contextHotkeys", function="context_Alt_D")
	create_runTimeCmd(module="contextHotkeys", function="context_copy")
	create_runTimeCmd(module="contextHotkeys", function="context_paste")
	create_runTimeCmd(module="importExport", function="import_clipboard_usd")
	create_runTimeCmd(module="importExport", function="export_clipboard_usd")
	create_runTimeCmd(module="importExport", function="import_nvil_obj")
	create_runTimeCmd(module="importExport", function="export_nvil_obj")
	create_runTimeCmd(module="file", function="open_file_from_clipboard")
	create_runTimeCmd(module="viewport", name='toggle_viewport_joints_display', function="toggle_model_panel_option", params='"joints"')
	create_runTimeCmd(module="viewport", name='toggle_viewport_meshes_display', function="toggle_model_panel_option", params='"polymeshes"')
	create_runTimeCmd(module="viewport", name='toggle_viewport_curves_display', function="toggle_model_panel_option", params='"nurbsCurves"')
	create_runTimeCmd(module="viewport", function="flat_lighting")
	create_runTimeCmd(module="tools.flipLayer", function="toggle_before_after_layer")
	create_runTimeCmd(name="RIn_Renamer", module="tools.renamer", function="start", reload=True)
	create_runTimeCmd(name="RIn_Quick_Material", module="tools.quickMaterial", function="start")
	create_runTimeCmd(name="RIn_Quick_Blendshapes", module="tools.quickBlendshapes", function="start", reload=True)
	create_runTimeCmd(name="RIn_Copy", module="tools.copy", function="start_copy")  # tools.copy has global CLIPBOARD data, if reload will reset the data everytime
	create_runTimeCmd(name="RIn_Paste", module="tools.copy", function="start_paste")
	create_runTimeCmd(module="temp", function="temp_1", reload=True)
	create_runTimeCmd(module="temp", function="temp_2", reload=True)

	# pie menus
	create_runTimeCmd(name="PieMenu_Main", pieModule='mainPieMenu')
	create_runTimeCmd(name="PieMenu_UI_Elements", pieModule='uiElementsPieMenu')
	create_runTimeCmd(name="PieMenu_Selections", pieModule='selectionsPieMenu')
	create_runTimeCmd(name="PieMenu_Normals", pieModule='normalsPieMenu')
	create_runTimeCmd(name="PieMenu_Deformers", pieModule='deformersPieMenu')
	create_runTimeCmd(name="PieMenu_Displays", pieModule='displaysPieMenu')
	create_runTimeCmd(name="PieMenu_Editors", pieModule='editorsPieMenu')
	create_runTimeCmd(name="PieMenu_Symmetry", pieModule='symmetryPieMenu')
	create_runTimeCmd(name="PieMenu_mGear", pieModule='mgearPieMenu')

def remove_RIn_cmds():
	# Delete the whole category
	user_nameCommands = cmds.runTimeCommand(q=True, userCommandArray=True)
	for unc in user_nameCommands:
		if cmds.runTimeCommand(unc, q=True, category=True) == CATEGORY_NAME:
			cmds.runTimeCommand(unc, e=True, delete=True)

def remove_zoo_cmds():
	# Delete the whole category
	user_nameCommands = cmds.runTimeCommand(q=True, userCommandArray=True)
	for unc in user_nameCommands:
		if cmds.runTimeCommand(unc, q=True, category=True).lower().startswith('custom scripts.zoo'):
			cmds.runTimeCommand(unc, e=True, delete=True)
