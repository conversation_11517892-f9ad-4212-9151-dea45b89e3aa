import maya.mel as mel
import maya.cmds as cmds

from .objects import delete_node
from .utils import obj_is_type, in_component_mode, in_vertex_mode, duplicate_name

def hotkey_A():
	# UV Editor
	if cmds.getPanel(underPointer=True) and 'polyTexturePlacementPanel' in cmds.getPanel(underPointer=True):
		mel.eval('selectMode -component;SelectUVMask;')

	# Paint Attributes
	elif cmds.currentCtx() in ['artAttrContext', 'artAttrBlendShapeContext']:
		# Toggle color feedback
		current_ctx = cmds.currentCtx()
		colorfeedback_stat = cmds.artAttrCtx(current_ctx, q=True, colorfeedback=True)
		cmds.artAttrCtx(current_ctx, e=True, colorfeedback=not colorfeedback_stat)

	else:
		selections = cmds.ls(sl=True, flatten=True)
		if selections:
			first_selection = selections[0]
			if '.' in first_selection:
				mel.eval('selectMode -component; SelectVertexMask;')
			if obj_is_type(first_selection, 'lattice'):
				mel.eval('selectMode -component; selectType -latticePoint true;')
			elif obj_is_type(first_selection, 'nurbsCurve'):
				mel.eval('selectMode -component; selectType -controlVertex true;')
			mel.eval('selectMode -component; SelectVertexMask;')
		else:
			if cmds.selectMode(q=True, component=True) or cmds.selectType(q=True, allComponents=True):
				mel.eval('selectMode -component; SelectVertexMask;')
			else:
				mel.eval('dR_DoCmd("selectAll")')

def context_Duplicate():
	def selections_are_all_transforms(selections):
		for obj in selections:
			if not cmds.nodeType(obj) == 'transform':
				return False
		return True

	panels_under_pointer = cmds.getPanel(underPointer=True)
	if panels_under_pointer and 'hyperShadePanel' in panels_under_pointer:
		mel.eval('hyperShadePanelMenuCommand("hyperShadePanel1", "duplicateShadingNetwork")')
	else:
		selections = cmds.ls(sl=True, flatten=True)
		if selections:
			if '.f' in selections[0]:
				cmds.polyExtrudeFacet(constructionHistory=True, keepFacesTogether=True)
				return
			elif selections_are_all_transforms(selections):
				dups = []
				for obj in selections:
					new_name = duplicate_name(obj)
					dup = cmds.duplicate(obj)[0]
					dup = cmds.rename(dup, new_name)
					dups.append(dup)
				cmds.select(dups, replace=True)
				return
		mel.eval('performDuplicate false;')

def context_Delete():
	selection = cmds.ls(sl=True, flatten=True)
	if (
		selection and
		any('.' in item for item in selection) and
		cmds.selectMode(q=True, component=True) and
		cmds.selectType(q=True, allComponents=True) and
		cmds.selectType(q=True, vertex=True) 
		):
		mel.eval('performPolyDeleteElements')
	else:
		mel.eval('doDelete')

def context_Alt_C():
	if cmds.currentCtx().startswith('artAttr'):
		mel.eval('artToggleColorFeedback')
	else:
		if in_component_mode():
			mel.eval('polyPerformAction polyConnectComponents n 0')
		else:
			print("Please assign a command to object mode")

def context_Alt_D():
	if in_component_mode():
		mel.eval('MergeToCenter')
	else:
		delete_node()

def context_copy():
	if in_vertex_mode():
		mel.eval('doCopyVertexWeightsArgList 1 { "1" };')
	else:
		mel.eval('cutCopyPaste "copy"')

def context_paste():
	if in_vertex_mode():
		mel.eval('doPasteVertexWeightsArgList 1 { "1" };')
	else:
		mel.eval('cutCopyPaste "paste"')
