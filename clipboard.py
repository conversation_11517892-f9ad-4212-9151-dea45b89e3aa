import os

from PySide6 import QtWidgets

def get_filepaths_from_clipboard():
	"""
	Get file paths from clipboard

	:return: a list of file paths or else an empty list
	"""
	clipboard = QtWidgets.QApplication.clipboard()
	clipboard_data = clipboard.mimeData()
	files = []
	if clipboard_data.hasUrls():
		urls = clipboard_data.urls()
		for url in urls:
			files.append(url.toLocalFile())
	elif clipboard_data.hasText():
		texts = clipboard_data.text().split("\n")
		for line in texts:
			if os.path.isfile(line):
				files.append(line)
	return files

